import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from 'styled-components';
import { GlobalStyles, theme } from './styles/GlobalStyles';
import Login from './components/Auth/Login';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import AgentDashboard from './components/Dashboard/AgentDashboard';
import SupervisorDashboard from './components/Dashboard/SupervisorDashboard';
import AdminDashboard from './components/Dashboard/AdminDashboard';
import LeadDetails from './components/Leads/LeadDetails';
import VerificationForm from './components/Verification/VerificationForm';
import DocumentUpload from './components/Documents/DocumentUpload';
import CreateLead from './components/Admin/CreateLead';
import UserManagement from './components/Admin/UserManagement';
import AgentTasks from './components/Agent/AgentTasks';
import AgentCompleted from './components/Agent/AgentCompleted';
import AgentReports from './components/Agent/AgentReports';
import SupervisorReview from './components/Supervisor/SupervisorReview';
import SupervisorReports from './components/Supervisor/SupervisorReports';
import AdminReports from './components/Admin/AdminReports';
import LeadsList from './components/Leads/LeadsList';
import UserProfile from './components/Profile/UserProfile';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* Agent Routes */}
            <Route
              path="/agent/dashboard"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <AgentDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agent/tasks"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <AgentTasks />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agent/completed"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <AgentCompleted />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agent/reports"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <AgentReports />
                </ProtectedRoute>
              }
            />

            {/* Supervisor Routes */}
            <Route
              path="/supervisor/dashboard"
              element={
                <ProtectedRoute allowedRoles={['Supervisor']}>
                  <SupervisorDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/supervisor/review"
              element={
                <ProtectedRoute allowedRoles={['Supervisor']}>
                  <SupervisorReview />
                </ProtectedRoute>
              }
            />
            <Route
              path="/supervisor/reports"
              element={
                <ProtectedRoute allowedRoles={['Supervisor']}>
                  <SupervisorReports />
                </ProtectedRoute>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute allowedRoles={['Admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/create-lead"
              element={
                <ProtectedRoute allowedRoles={['Admin']}>
                  <CreateLead />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/users"
              element={
                <ProtectedRoute allowedRoles={['Admin']}>
                  <UserManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/leads"
              element={
                <ProtectedRoute allowedRoles={['Admin']}>
                  <LeadsList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/reports"
              element={
                <ProtectedRoute allowedRoles={['Admin']}>
                  <AdminReports />
                </ProtectedRoute>
              }
            />

            {/* Shared Routes */}
            <Route
              path="/lead/:id"
              element={
                <ProtectedRoute allowedRoles={['Agent', 'Supervisor', 'Admin']}>
                  <LeadDetails />
                </ProtectedRoute>
              }
            />

            <Route
              path="/lead/:id/verification"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <VerificationForm />
                </ProtectedRoute>
              }
            />

            <Route
              path="/lead/:id/documents"
              element={
                <ProtectedRoute allowedRoles={['Agent']}>
                  <DocumentUpload />
                </ProtectedRoute>
              }
            />

            {/* Profile Route */}
            <Route
              path="/profile"
              element={
                <ProtectedRoute allowedRoles={['Agent', 'Supervisor', 'Admin']}>
                  <UserProfile />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
