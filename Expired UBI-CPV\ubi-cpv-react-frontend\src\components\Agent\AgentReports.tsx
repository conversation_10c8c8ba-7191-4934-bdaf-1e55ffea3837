import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService } from '../../services/apiService';

const ReportsContainer = styled.div`
  display: grid;
  gap: 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #007E3A, #005a2a);
  color: white;
`;

const StatValue = styled.div`
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
`;

const ChartContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled(Card)`
  padding: 20px;
`;

const ChartTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
  text-align: center;
`;

const PerformanceBar = styled.div<{ percentage: number; color: string }>`
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
  
  &::after {
    content: '';
    display: block;
    width: ${props => props.percentage}%;
    height: 100%;
    background-color: ${props => props.color};
    transition: width 0.3s ease;
  }
`;

const PerformanceItem = styled.div`
  margin-bottom: 15px;
`;

const PerformanceLabel = styled.div`
  display: flex;
  justify-content: between;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const RecentActivity = styled.div`
  max-height: 300px;
  overflow-y: auto;
`;

const ActivityItem = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityInfo = styled.div`
  flex: 1;
`;

const ActivityTitle = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

const ActivityDate = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textLight};
`;

const AgentReports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('week');
  const [stats, setStats] = useState({
    totalTasks: 0,
    completedTasks: 0,
    approvedTasks: 0,
    rejectedTasks: 0,
    averageTime: 0,
    efficiency: 0,
  });
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    loadReportsData();
  }, [timeFilter]);

  const loadReportsData = async () => {
    try {
      setLoading(true);
      
      // In a real app, this would be a dedicated reports API endpoint
      const [dashboardStats] = await Promise.all([
        apiService.getAgentDashboardStats(),
      ]);

      setStats({
        totalTasks: dashboardStats.totalAssigned || 0,
        completedTasks: dashboardStats.completedLeads || 0,
        approvedTasks: dashboardStats.completedLeads || 0,
        rejectedTasks: dashboardStats.rejectedLeads || 0,
        averageTime: 2.5, // Mock data
        efficiency: 85, // Mock data
      });

      // Mock recent activity
      setRecentActivity([
        {
          id: 1,
          title: 'Completed verification for John Doe',
          date: '2024-01-16T14:30:00Z',
          type: 'completed',
        },
        {
          id: 2,
          title: 'Started verification for Jane Smith',
          date: '2024-01-16T10:15:00Z',
          type: 'started',
        },
        {
          id: 3,
          title: 'Uploaded documents for Alice Johnson',
          date: '2024-01-15T16:45:00Z',
          type: 'upload',
        },
      ]);

    } catch (error) {
      console.error('Error loading reports data:', error);
      // Use mock data
      setStats({
        totalTasks: 15,
        completedTasks: 12,
        approvedTasks: 10,
        rejectedTasks: 2,
        averageTime: 2.5,
        efficiency: 85,
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },
    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },
    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },
    { icon: '📊', label: 'Reports', active: true },
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCompletionRate = () => {
    return stats.totalTasks > 0 ? Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0;
  };

  const getApprovalRate = () => {
    return stats.completedTasks > 0 ? Math.round((stats.approvedTasks / stats.completedTasks) * 100) : 0;
  };

  if (loading) {
    return (
      <DashboardLayout title="Performance Reports" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Performance Reports" navigationItems={navigationItems}>
      <ReportsContainer>
        <FilterContainer>
          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </FilterSelect>
          
          <Button variant="outline" onClick={() => window.print()}>
            📄 Export Report
          </Button>
        </FilterContainer>

        {/* Key Metrics */}
        <StatsGrid>
          <StatCard>
            <StatValue>{stats.totalTasks}</StatValue>
            <StatLabel>Total Tasks Assigned</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.completedTasks}</StatValue>
            <StatLabel>Tasks Completed</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.averageTime}</StatValue>
            <StatLabel>Avg. Days per Task</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.efficiency}%</StatValue>
            <StatLabel>Efficiency Score</StatLabel>
          </StatCard>
        </StatsGrid>

        {/* Performance Charts */}
        <ChartContainer>
          <ChartCard>
            <ChartTitle>Task Performance</ChartTitle>
            <PerformanceItem>
              <PerformanceLabel>
                <span>Completion Rate</span>
                <span>{getCompletionRate()}%</span>
              </PerformanceLabel>
              <PerformanceBar percentage={getCompletionRate()} color="#2e7d32" />
            </PerformanceItem>
            <PerformanceItem>
              <PerformanceLabel>
                <span>Approval Rate</span>
                <span>{getApprovalRate()}%</span>
              </PerformanceLabel>
              <PerformanceBar percentage={getApprovalRate()} color="#007E3A" />
            </PerformanceItem>
            <PerformanceItem>
              <PerformanceLabel>
                <span>Efficiency</span>
                <span>{stats.efficiency}%</span>
              </PerformanceLabel>
              <PerformanceBar percentage={stats.efficiency} color="#FFD100" />
            </PerformanceItem>
          </ChartCard>

          <ChartCard>
            <ChartTitle>Recent Activity</ChartTitle>
            <RecentActivity>
              {recentActivity.map((activity) => (
                <ActivityItem key={activity.id}>
                  <ActivityInfo>
                    <ActivityTitle>{activity.title}</ActivityTitle>
                    <ActivityDate>{formatDate(activity.date)}</ActivityDate>
                  </ActivityInfo>
                </ActivityItem>
              ))}
            </RecentActivity>
          </ChartCard>
        </ChartContainer>

        {/* Summary Card */}
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Performance Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px' }}>
            <div>
              <h4>Tasks Overview</h4>
              <p>Total Assigned: {stats.totalTasks}</p>
              <p>Completed: {stats.completedTasks}</p>
              <p>Pending: {stats.totalTasks - stats.completedTasks}</p>
            </div>
            <div>
              <h4>Quality Metrics</h4>
              <p>Approved: {stats.approvedTasks}</p>
              <p>Rejected: {stats.rejectedTasks}</p>
              <p>Success Rate: {getApprovalRate()}%</p>
            </div>
            <div>
              <h4>Efficiency</h4>
              <p>Avg. Time: {stats.averageTime} days</p>
              <p>Efficiency: {stats.efficiency}%</p>
              <p>Productivity: High</p>
            </div>
          </div>
        </Card>
      </ReportsContainer>
    </DashboardLayout>
  );
};

export default AgentReports;
