import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem, User } from '../../services/apiService';

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.theme.colors.mediumGray};
  }
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'new':
        return `
          background-color: #e3f2fd;
          color: #0d47a1;
        `;
      case 'assigned':
        return `
          background-color: #fff3e0;
          color: #e65100;
        `;
      case 'in-progress':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
`;

const PageButton = styled.button<{ active?: boolean }>`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  background: ${props => props.active ? props.theme.colors.primary : 'white'};
  color: ${props => props.active ? 'white' : props.theme.colors.textDark};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  
  &:hover {
    background: ${props => props.active ? props.theme.colors.primary : props.theme.colors.lightGray};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AssignModal = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'flex' : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
`;

const ModalActions = styled.div`
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
`;

const LeadsList: React.FC = () => {
  const [leads, setLeads] = useState<LeadListItem[]>([]);
  const [agents, setAgents] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLead, setSelectedLead] = useState<LeadListItem | null>(null);
  const [selectedAgent, setSelectedAgent] = useState('');
  const [sortField, setSortField] = useState('createdDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const pageSize = 20;
  const navigate = useNavigate();

  useEffect(() => {
    loadLeads();
    loadAgents();
  }, [currentPage, statusFilter, sortField, sortDirection]);

  const loadLeads = async () => {
    try {
      setLoading(true);
      const response = await apiService.getLeads(
        currentPage,
        pageSize,
        statusFilter === 'all' ? undefined : statusFilter
      );
      setLeads(response.data || []);
      setTotalPages(Math.ceil((response.totalCount || 0) / pageSize));
    } catch (error) {
      console.error('Error loading leads:', error);
      // Mock data for demo
      setLeads([
        {
          leadId: 1,
          customerName: 'John Doe',
          mobileNumber: '9876543210',
          loanType: 'Personal Loan',
          status: 'new',
          createdDate: '2024-01-15T10:30:00Z',
          createdByName: 'Admin User',
          assignedToName: '',
          documentCount: 0,
          croppedImageCount: 0,
        },
        {
          leadId: 2,
          customerName: 'Jane Smith',
          mobileNumber: '9876543211',
          loanType: 'Home Loan',
          status: 'assigned',
          createdDate: '2024-01-14T09:15:00Z',
          assignedDate: '2024-01-14T10:00:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Agent Johnson',
          documentCount: 2,
          croppedImageCount: 1,
        },
      ]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const loadAgents = async () => {
    try {
      const users = await apiService.getUsers();
      setAgents(users.filter(user => user.role === 'Agent' && user.isActive));
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  const filteredLeads = leads.filter(lead =>
    lead.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.mobileNumber.includes(searchTerm) ||
    lead.loanType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (lead.assignedToName && lead.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/admin/dashboard') },
    { icon: '👥', label: 'Users', onClick: () => navigate('/admin/users') },
    { icon: '📋', label: 'Leads', active: true },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/admin/reports') },
    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },
  ];

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleAssignLead = (lead: LeadListItem) => {
    setSelectedLead(lead);
    setSelectedAgent('');
  };

  const submitAssignment = async () => {
    if (!selectedLead || !selectedAgent) return;

    try {
      await apiService.assignLead(selectedLead.leadId, parseInt(selectedAgent), 'Assigned by admin');
      
      // Update local state
      setLeads(leads => 
        leads.map(lead => 
          lead.leadId === selectedLead.leadId 
            ? { 
                ...lead, 
                status: 'assigned',
                assignedToName: agents.find(a => a.userId === parseInt(selectedAgent))?.firstName + ' ' + 
                               agents.find(a => a.userId === parseInt(selectedAgent))?.lastName,
                assignedDate: new Date().toISOString()
              }
            : lead
        )
      );

      setSelectedLead(null);
      setSelectedAgent('');
    } catch (error) {
      console.error('Error assigning lead:', error);
      alert('Failed to assign lead');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  if (loading) {
    return (
      <DashboardLayout title="All Leads" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="All Leads" navigationItems={navigationItems}>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 style={{ color: '#007E3A' }}>Lead Management</h2>
          <Button onClick={() => navigate('/admin/create-lead')}>
            + Create New Lead
          </Button>
        </div>

        <FilterContainer>
          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="assigned">Assigned</option>
            <option value="in-progress">In Progress</option>
            <option value="pending-review">Pending Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </FilterSelect>
          
          <SearchInput
            type="text"
            placeholder="Search by customer, mobile, loan type, or agent..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </FilterContainer>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader onClick={() => handleSort('leadId')}>
                  ID {getSortIcon('leadId')}
                </TableHeader>
                <TableHeader onClick={() => handleSort('customerName')}>
                  Customer {getSortIcon('customerName')}
                </TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader onClick={() => handleSort('loanType')}>
                  Loan Type {getSortIcon('loanType')}
                </TableHeader>
                <TableHeader onClick={() => handleSort('status')}>
                  Status {getSortIcon('status')}
                </TableHeader>
                <TableHeader>Assigned To</TableHeader>
                <TableHeader onClick={() => handleSort('createdDate')}>
                  Created {getSortIcon('createdDate')}
                </TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {filteredLeads.map((lead) => (
                <TableRow key={lead.leadId}>
                  <TableCell>#{lead.leadId}</TableCell>
                  <TableCell style={{ fontWeight: '500' }}>{lead.customerName}</TableCell>
                  <TableCell>{lead.mobileNumber}</TableCell>
                  <TableCell>{lead.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={lead.status}>
                      {lead.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>{lead.assignedToName || 'Unassigned'}</TableCell>
                  <TableCell>{formatDate(lead.createdDate)}</TableCell>
                  <TableCell>
                    <ActionButtons>
                      <Button
                        size="sm"
                        onClick={() => navigate(`/lead/${lead.leadId}`)}
                      >
                        View
                      </Button>
                      {lead.status === 'new' && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleAssignLead(lead)}
                        >
                          Assign
                        </Button>
                      )}
                    </ActionButtons>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {filteredLeads.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            {searchTerm ? 'No leads found matching your search.' : 'No leads found.'}
          </div>
        )}

        {/* Pagination */}
        <Pagination>
          <PageButton 
            onClick={() => setCurrentPage(1)} 
            disabled={currentPage === 1}
          >
            First
          </PageButton>
          <PageButton 
            onClick={() => setCurrentPage(currentPage - 1)} 
            disabled={currentPage === 1}
          >
            Previous
          </PageButton>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = Math.max(1, currentPage - 2) + i;
            if (page <= totalPages) {
              return (
                <PageButton
                  key={page}
                  active={page === currentPage}
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </PageButton>
              );
            }
            return null;
          })}
          
          <PageButton 
            onClick={() => setCurrentPage(currentPage + 1)} 
            disabled={currentPage === totalPages}
          >
            Next
          </PageButton>
          <PageButton 
            onClick={() => setCurrentPage(totalPages)} 
            disabled={currentPage === totalPages}
          >
            Last
          </PageButton>
        </Pagination>
      </Card>

      {/* Assignment Modal */}
      <AssignModal isOpen={!!selectedLead}>
        <ModalContent>
          <ModalTitle>Assign Lead - {selectedLead?.customerName}</ModalTitle>
          
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
              Select Agent:
            </label>
            <FilterSelect
              value={selectedAgent}
              onChange={(e) => setSelectedAgent(e.target.value)}
              style={{ width: '100%' }}
            >
              <option value="">Choose an agent...</option>
              {agents.map(agent => (
                <option key={agent.userId} value={agent.userId}>
                  {agent.firstName} {agent.lastName} ({agent.username})
                </option>
              ))}
            </FilterSelect>
          </div>

          <ModalActions>
            <Button
              variant="outline"
              onClick={() => {
                setSelectedLead(null);
                setSelectedAgent('');
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitAssignment}
              disabled={!selectedAgent}
            >
              Assign Lead
            </Button>
          </ModalActions>
        </ModalContent>
      </AssignModal>
    </DashboardLayout>
  );
};

export default LeadsList;
