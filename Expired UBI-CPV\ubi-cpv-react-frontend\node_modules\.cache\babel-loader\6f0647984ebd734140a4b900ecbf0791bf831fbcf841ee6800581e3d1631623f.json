{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Agent\\\\AgentCompleted.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c = StatsContainer;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n`;\n_c2 = StatCard;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n_c3 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n_c4 = StatLabel;\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c5 = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c6 = FilterSelect;\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c7 = SearchInput;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c8 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c9 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c0 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c1 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c10 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.status) {\n    case 'approved':\n    case 'completed':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    case 'rejected':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    case 'pending-review':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c11 = StatusBadge;\nconst AgentCompleted = () => {\n  _s();\n  const [completedTasks, setCompletedTasks] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [stats, setStats] = useState({\n    totalCompleted: 0,\n    approved: 0,\n    rejected: 0,\n    pendingReview: 0\n  });\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadCompletedTasks();\n  }, [statusFilter]);\n  const loadCompletedTasks = async () => {\n    try {\n      setLoading(true);\n\n      // Load completed tasks based on filter\n      const statuses = statusFilter === 'all' ? ['approved', 'rejected', 'pending-review'] : [statusFilter];\n      const allTasks = [];\n      for (const status of statuses) {\n        try {\n          const response = await apiService.getLeads(1, 100, status);\n          allTasks.push(...(response.data || []));\n        } catch (error) {\n          console.error(`Error loading ${status} tasks:`, error);\n        }\n      }\n      setCompletedTasks(allTasks);\n\n      // Calculate stats\n      const approved = allTasks.filter(task => task.status === 'approved').length;\n      const rejected = allTasks.filter(task => task.status === 'rejected').length;\n      const pendingReview = allTasks.filter(task => task.status === 'pending-review').length;\n      setStats({\n        totalCompleted: allTasks.length,\n        approved,\n        rejected,\n        pendingReview\n      });\n    } catch (error) {\n      console.error('Error loading completed tasks:', error);\n      // Mock data for demo\n      const mockTasks = [{\n        leadId: 3,\n        customerName: 'Alice Johnson',\n        mobileNumber: '9876543212',\n        loanType: 'Car Loan',\n        status: 'approved',\n        createdDate: '2024-01-10T08:00:00Z',\n        assignedDate: '2024-01-10T09:00:00Z',\n        submittedDate: '2024-01-12T16:30:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Current Agent',\n        documentCount: 3,\n        croppedImageCount: 2\n      }, {\n        leadId: 4,\n        customerName: 'Bob Wilson',\n        mobileNumber: '9876543213',\n        loanType: 'Personal Loan',\n        status: 'rejected',\n        createdDate: '2024-01-08T10:15:00Z',\n        assignedDate: '2024-01-08T11:00:00Z',\n        submittedDate: '2024-01-09T14:20:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Current Agent',\n        documentCount: 2,\n        croppedImageCount: 1\n      }];\n      setCompletedTasks(mockTasks);\n      setStats({\n        totalCompleted: 2,\n        approved: 1,\n        rejected: 1,\n        pendingReview: 0\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredTasks = completedTasks.filter(task => task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || task.mobileNumber.includes(searchTerm) || task.loanType.toLowerCase().includes(searchTerm.toLowerCase()));\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/agent/dashboard')\n  }, {\n    icon: '📋',\n    label: 'My Tasks',\n    onClick: () => navigate('/agent/tasks')\n  }, {\n    icon: '✅',\n    label: 'Completed',\n    active: true\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    onClick: () => navigate('/agent/reports')\n  }];\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  const calculateDuration = (startDate, endDate) => {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Completed Tasks\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Completed Tasks\",\n    navigationItems: navigationItems,\n    children: [/*#__PURE__*/_jsxDEV(StatsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatValue, {\n          children: stats.totalCompleted\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Total Completed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatValue, {\n          style: {\n            color: '#2e7d32'\n          },\n          children: stats.approved\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Approved\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatValue, {\n          style: {\n            color: '#c62828'\n          },\n          children: stats.rejected\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Rejected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        children: [/*#__PURE__*/_jsxDEV(StatValue, {\n          style: {\n            color: '#4a148c'\n          },\n          children: stats.pendingReview\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"Pending Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '20px',\n          color: '#007E3A'\n        },\n        children: \"Completed Verifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: statusFilter,\n          onChange: e => setStatusFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"approved\",\n            children: \"Approved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"rejected\",\n            children: \"Rejected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending-review\",\n            children: \"Pending Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchInput, {\n          type: \"text\",\n          placeholder: \"Search by customer name, mobile, or loan type...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Mobile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Submitted Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Duration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredTasks.map(task => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.mobileNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.loanType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  status: task.status,\n                  children: task.status.replace('-', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.submittedDate ? formatDate(task.submittedDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.assignedDate && task.submittedDate ? calculateDuration(task.assignedDate, task.submittedDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"sm\",\n                  onClick: () => navigate(`/lead/${task.leadId}`),\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)]\n            }, task.leadId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), filteredTasks.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: searchTerm ? 'No completed tasks found matching your search.' : 'No completed tasks yet.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentCompleted, \"IfZm4N9MJCwDCmcfWHA/sMspCiE=\", false, function () {\n  return [useNavigate];\n});\n_c12 = AgentCompleted;\nexport default AgentCompleted;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"StatsContainer\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"StatValue\");\n$RefreshReg$(_c4, \"StatLabel\");\n$RefreshReg$(_c5, \"FilterContainer\");\n$RefreshReg$(_c6, \"FilterSelect\");\n$RefreshReg$(_c7, \"SearchInput\");\n$RefreshReg$(_c8, \"TableContainer\");\n$RefreshReg$(_c9, \"Table\");\n$RefreshReg$(_c0, \"TableHeader\");\n$RefreshReg$(_c1, \"TableCell\");\n$RefreshReg$(_c10, \"TableRow\");\n$RefreshReg$(_c11, \"StatusBadge\");\n$RefreshReg$(_c12, \"AgentCompleted\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "StatsContainer", "div", "_c", "StatCard", "_c2", "StatValue", "props", "theme", "colors", "textDark", "_c3", "StatLabel", "textLight", "_c4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c5", "FilterSelect", "select", "mediumGray", "borderRadius", "sm", "primary", "_c6", "SearchInput", "input", "_c7", "TableContainer", "_c8", "Table", "table", "_c9", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c0", "TableCell", "td", "_c1", "TableRow", "tr", "_c10", "StatusBadge", "span", "status", "_c11", "AgentCompleted", "_s", "completedTasks", "setCompletedTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "stats", "setStats", "totalCompleted", "approved", "rejected", "pendingReview", "navigate", "loadCompletedTasks", "statuses", "allTasks", "response", "getLeads", "push", "data", "error", "console", "filter", "task", "length", "mockTasks", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "submittedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "formatDate", "dateString", "Date", "toLocaleDateString", "calculateDuration", "startDate", "endDate", "start", "end", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "marginBottom", "value", "onChange", "e", "target", "type", "placeholder", "map", "replace", "toUpperCase", "size", "textAlign", "padding", "_c12", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentCompleted.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 5px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  color: ${props => props.theme.colors.textLight};\n  font-weight: 500;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'approved':\n      case 'completed':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst AgentCompleted: React.FC = () => {\n  const [completedTasks, setCompletedTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [stats, setStats] = useState({\n    totalCompleted: 0,\n    approved: 0,\n    rejected: 0,\n    pendingReview: 0,\n  });\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadCompletedTasks();\n  }, [statusFilter]);\n\n  const loadCompletedTasks = async () => {\n    try {\n      setLoading(true);\n      \n      // Load completed tasks based on filter\n      const statuses = statusFilter === 'all' \n        ? ['approved', 'rejected', 'pending-review'] \n        : [statusFilter];\n      \n      const allTasks: LeadListItem[] = [];\n      \n      for (const status of statuses) {\n        try {\n          const response = await apiService.getLeads(1, 100, status);\n          allTasks.push(...(response.data || []));\n        } catch (error) {\n          console.error(`Error loading ${status} tasks:`, error);\n        }\n      }\n      \n      setCompletedTasks(allTasks);\n      \n      // Calculate stats\n      const approved = allTasks.filter(task => task.status === 'approved').length;\n      const rejected = allTasks.filter(task => task.status === 'rejected').length;\n      const pendingReview = allTasks.filter(task => task.status === 'pending-review').length;\n      \n      setStats({\n        totalCompleted: allTasks.length,\n        approved,\n        rejected,\n        pendingReview,\n      });\n      \n    } catch (error) {\n      console.error('Error loading completed tasks:', error);\n      // Mock data for demo\n      const mockTasks = [\n        {\n          leadId: 3,\n          customerName: 'Alice Johnson',\n          mobileNumber: '9876543212',\n          loanType: 'Car Loan',\n          status: 'approved',\n          createdDate: '2024-01-10T08:00:00Z',\n          assignedDate: '2024-01-10T09:00:00Z',\n          submittedDate: '2024-01-12T16:30:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n        {\n          leadId: 4,\n          customerName: 'Bob Wilson',\n          mobileNumber: '9876543213',\n          loanType: 'Personal Loan',\n          status: 'rejected',\n          createdDate: '2024-01-08T10:15:00Z',\n          assignedDate: '2024-01-08T11:00:00Z',\n          submittedDate: '2024-01-09T14:20:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ];\n      \n      setCompletedTasks(mockTasks);\n      setStats({\n        totalCompleted: 2,\n        approved: 1,\n        rejected: 1,\n        pendingReview: 0,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = completedTasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },\n    { icon: '✅', label: 'Completed', active: true },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const calculateDuration = (startDate: string, endDate: string) => {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Completed Tasks\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Completed Tasks\" navigationItems={navigationItems}>\n      {/* Stats Cards */}\n      <StatsContainer>\n        <StatCard>\n          <StatValue>{stats.totalCompleted}</StatValue>\n          <StatLabel>Total Completed</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#2e7d32' }}>{stats.approved}</StatValue>\n          <StatLabel>Approved</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#c62828' }}>{stats.rejected}</StatValue>\n          <StatLabel>Rejected</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue style={{ color: '#4a148c' }}>{stats.pendingReview}</StatValue>\n          <StatLabel>Pending Review</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Completed Verifications</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"all\">All Completed</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"pending-review\">Pending Review</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer name, mobile, or loan type...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Submitted Date</TableHeader>\n                <TableHeader>Duration</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {task.assignedDate && task.submittedDate \n                      ? calculateDuration(task.assignedDate, task.submittedDate)\n                      : '-'\n                    }\n                  </TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => navigate(`/lead/${task.leadId}`)}\n                    >\n                      View Details\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No completed tasks found matching your search.' : 'No completed tasks yet.'}\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentCompleted;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAsB,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,cAAc,GAAGR,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,QAAQ,GAAGX,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GANID,QAAQ;AAQd,MAAME,SAAS,GAAGb,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,WAAWK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,QAAQ;AAC/C,CAAC;AAACC,GAAA,GALIL,SAAS;AAOf,MAAMM,SAAS,GAAGnB,MAAM,CAACS,GAAG;AAC5B;AACA,WAAWK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,SAAS;AAChD;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,eAAe,GAAGtB,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAGxB,MAAM,CAACyB,MAAM;AAClC;AACA,sBAAsBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,UAAU;AAC5D,mBAAmBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,OAAO;AACvD;AACA;AACA,CAAC;AAACC,GAAA,GAXIN,YAAY;AAalB,MAAMO,WAAW,GAAG/B,MAAM,CAACgC,KAAK;AAChC;AACA;AACA;AACA,sBAAsBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,UAAU;AAC5D,mBAAmBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,OAAO;AACvD;AACA;AACA,CAAC;AAACI,GAAA,GAZIF,WAAW;AAcjB,MAAMG,cAAc,GAAGlC,MAAM,CAACS,GAAG;AACjC;AACA,CAAC;AAAC0B,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAGpC,MAAM,CAACqC,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAGvC,MAAM,CAACwC,EAAE;AAC7B;AACA;AACA,6BAA6B1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS;AAClE,sBAAsB3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC0B,QAAQ;AAC1D;AACA,WAAW5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2B,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAG7C,MAAM,CAAC8C,EAAE;AAC3B;AACA;AACA,6BAA6BhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGhD,MAAM,CAACiD,EAAE;AAC1B;AACA,wBAAwBnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,SAAS;AAC7D;AACA,CAAC;AAACS,IAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAGnD,MAAM,CAACoD,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAItC,KAAK,IAAI;EACT,QAAQA,KAAK,CAACuC,MAAM;IAClB,KAAK,UAAU;IACf,KAAK,WAAW;MACd,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,gBAAgB;MACnB,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GAhCIH,WAAW;AAkCjB,MAAMI,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAiB,EAAE,CAAC;EACxE,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoE,KAAK,EAAEC,QAAQ,CAAC,GAAGrE,QAAQ,CAAC;IACjCsE,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGxE,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd0E,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;EAElB,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMa,QAAQ,GAAGZ,YAAY,KAAK,KAAK,GACnC,CAAC,UAAU,EAAE,UAAU,EAAE,gBAAgB,CAAC,GAC1C,CAACA,YAAY,CAAC;MAElB,MAAMa,QAAwB,GAAG,EAAE;MAEnC,KAAK,MAAMrB,MAAM,IAAIoB,QAAQ,EAAE;QAC7B,IAAI;UACF,MAAME,QAAQ,GAAG,MAAMtE,UAAU,CAACuE,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAEvB,MAAM,CAAC;UAC1DqB,QAAQ,CAACG,IAAI,CAAC,IAAIF,QAAQ,CAACG,IAAI,IAAI,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB1B,MAAM,SAAS,EAAE0B,KAAK,CAAC;QACxD;MACF;MAEArB,iBAAiB,CAACgB,QAAQ,CAAC;;MAE3B;MACA,MAAMN,QAAQ,GAAGM,QAAQ,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7B,MAAM,KAAK,UAAU,CAAC,CAAC8B,MAAM;MAC3E,MAAMd,QAAQ,GAAGK,QAAQ,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7B,MAAM,KAAK,UAAU,CAAC,CAAC8B,MAAM;MAC3E,MAAMb,aAAa,GAAGI,QAAQ,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7B,MAAM,KAAK,gBAAgB,CAAC,CAAC8B,MAAM;MAEtFjB,QAAQ,CAAC;QACPC,cAAc,EAAEO,QAAQ,CAACS,MAAM;QAC/Bf,QAAQ;QACRC,QAAQ;QACRC;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,MAAMK,SAAS,GAAG,CAChB;QACEC,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,eAAe;QAC7BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,UAAU;QACpBnC,MAAM,EAAE,UAAU;QAClBoC,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,sBAAsB;QACrCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEV,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,YAAY;QAC1BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,eAAe;QACzBnC,MAAM,EAAE,UAAU;QAClBoC,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,sBAAsB;QACrCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,CACF;MAEDrC,iBAAiB,CAAC0B,SAAS,CAAC;MAC5BlB,QAAQ,CAAC;QACPC,cAAc,EAAE,CAAC;QACjBC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,aAAa,GAAGvC,cAAc,CAACwB,MAAM,CAACC,IAAI,IAC9CA,IAAI,CAACI,YAAY,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAAC,IAClEf,IAAI,CAACK,YAAY,CAACW,QAAQ,CAACnC,UAAU,CAAC,IACtCmB,IAAI,CAACM,QAAQ,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAC/D,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC/E;IAAE6B,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,cAAc;EAAE,CAAC,EAC1E;IAAE6B,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,WAAW;IAAEE,MAAM,EAAE;EAAK,CAAC,EAC/C;IAAEH,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,gBAAgB;EAAE,CAAC,CAC5E;EAED,MAAMiC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,SAAiB,EAAEC,OAAe,KAAK;IAChE,MAAMC,KAAK,GAAG,IAAIL,IAAI,CAACG,SAAS,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAIN,IAAI,CAACI,OAAO,CAAC;IAC7B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGL,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;IAC1D,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAO,GAAGI,QAAQ,OAAOA,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;EACtD,CAAC;EAED,IAAI1D,OAAO,EAAE;IACX,oBACEpD,OAAA,CAACN,eAAe;MAACsH,KAAK,EAAC,iBAAiB;MAACpB,eAAe,EAAEA,eAAgB;MAAAqB,QAAA,eACxEjH,OAAA,CAACH,cAAc;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACErH,OAAA,CAACN,eAAe;IAACsH,KAAK,EAAC,iBAAiB;IAACpB,eAAe,EAAEA,eAAgB;IAAAqB,QAAA,gBAExEjH,OAAA,CAACC,cAAc;MAAAgH,QAAA,gBACbjH,OAAA,CAACI,QAAQ;QAAA6G,QAAA,gBACPjH,OAAA,CAACM,SAAS;UAAA2G,QAAA,EAAEvD,KAAK,CAACE;QAAc;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7CrH,OAAA,CAACY,SAAS;UAAAqG,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACXrH,OAAA,CAACI,QAAQ;QAAA6G,QAAA,gBACPjH,OAAA,CAACM,SAAS;UAACgH,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAN,QAAA,EAAEvD,KAAK,CAACG;QAAQ;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpErH,OAAA,CAACY,SAAS;UAAAqG,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACXrH,OAAA,CAACI,QAAQ;QAAA6G,QAAA,gBACPjH,OAAA,CAACM,SAAS;UAACgH,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAN,QAAA,EAAEvD,KAAK,CAACI;QAAQ;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpErH,OAAA,CAACY,SAAS;UAAAqG,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACXrH,OAAA,CAACI,QAAQ;QAAA6G,QAAA,gBACPjH,OAAA,CAACM,SAAS;UAACgH,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAN,QAAA,EAAEvD,KAAK,CAACK;QAAa;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzErH,OAAA,CAACY,SAAS;UAAAqG,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEjBrH,OAAA,CAACL,IAAI;MAAAsH,QAAA,gBACHjH,OAAA;QAAIsH,KAAK,EAAE;UAAEE,YAAY,EAAE,MAAM;UAAED,KAAK,EAAE;QAAU,CAAE;QAAAN,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEnFrH,OAAA,CAACe,eAAe;QAAAkG,QAAA,gBACdjH,OAAA,CAACiB,YAAY;UAACwG,KAAK,EAAEnE,YAAa;UAACoE,QAAQ,EAAGC,CAAC,IAAKpE,eAAe,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAR,QAAA,gBAClFjH,OAAA;YAAQyH,KAAK,EAAC,KAAK;YAAAR,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CrH,OAAA;YAAQyH,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CrH,OAAA;YAAQyH,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CrH,OAAA;YAAQyH,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEfrH,OAAA,CAACwB,WAAW;UACVqG,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,kDAAkD;UAC9DL,KAAK,EAAEjE,UAAW;UAClBkE,QAAQ,EAAGC,CAAC,IAAKlE,aAAa,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAElBrH,OAAA,CAAC2B,cAAc;QAAAsF,QAAA,eACbjH,OAAA,CAAC6B,KAAK;UAAAoF,QAAA,gBACJjH,OAAA;YAAAiH,QAAA,eACEjH,OAAA;cAAAiH,QAAA,gBACEjH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnCrH,OAAA,CAACgC,WAAW;gBAAAiF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrH,OAAA;YAAAiH,QAAA,EACGxB,aAAa,CAACsC,GAAG,CAAEpD,IAAI,iBACtB3E,OAAA,CAACyC,QAAQ;cAAAwE,QAAA,gBACPjH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,EAAEtC,IAAI,CAACI;cAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,EAAEtC,IAAI,CAACK;cAAY;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,EAAEtC,IAAI,CAACM;cAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtCrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,eACRjH,OAAA,CAAC4C,WAAW;kBAACE,MAAM,EAAE6B,IAAI,CAAC7B,MAAO;kBAAAmE,QAAA,EAC9BtC,IAAI,CAAC7B,MAAM,CAACkF,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,EACPtC,IAAI,CAACS,aAAa,GAAGa,UAAU,CAACtB,IAAI,CAACS,aAAa,CAAC,GAAG;cAAG;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACZrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,EACPtC,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACS,aAAa,GACpCiB,iBAAiB,CAAC1B,IAAI,CAACQ,YAAY,EAAER,IAAI,CAACS,aAAa,CAAC,GACxD;cAAG;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eACZrH,OAAA,CAACsC,SAAS;gBAAA2E,QAAA,eACRjH,OAAA,CAACJ,MAAM;kBACLsI,IAAI,EAAC,IAAI;kBACTnC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,SAASW,IAAI,CAACG,MAAM,EAAE,CAAE;kBAAAmC,QAAA,EACjD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAzBC1C,IAAI,CAACG,MAAM;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BhB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhB5B,aAAa,CAACb,MAAM,KAAK,CAAC,iBACzB5E,OAAA;QAAKsH,KAAK,EAAE;UAAEa,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEb,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,EACjEzD,UAAU,GAAG,gDAAgD,GAAG;MAAyB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB,CAAC;AAACpE,EAAA,CAlOID,cAAwB;EAAA,QAWXxD,WAAW;AAAA;AAAA6I,IAAA,GAXxBrF,cAAwB;AAoO9B,eAAeA,cAAc;AAAC,IAAA7C,EAAA,EAAAE,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAsF,IAAA;AAAAC,YAAA,CAAAnI,EAAA;AAAAmI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}