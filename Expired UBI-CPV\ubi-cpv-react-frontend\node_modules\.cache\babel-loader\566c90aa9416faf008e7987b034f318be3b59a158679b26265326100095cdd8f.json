{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Agent\\\\AgentReports.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c = ReportsContainer;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c2 = StatsGrid;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n_c3 = StatCard;\nconst StatValue = styled.div`\n  font-size: 32px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n_c4 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n_c5 = StatLabel;\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c6 = ChartContainer;\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n_c7 = ChartCard;\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n_c8 = ChartTitle;\nconst PerformanceBar = styled.div`\n  width: 100%;\n  height: 20px;\n  background-color: #f0f0f0;\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 10px;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n_c9 = PerformanceBar;\nconst PerformanceItem = styled.div`\n  margin-bottom: 15px;\n`;\n_c0 = PerformanceItem;\nconst PerformanceLabel = styled.div`\n  display: flex;\n  justify-content: between;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: 500;\n`;\n_c1 = PerformanceLabel;\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c10 = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c11 = FilterSelect;\nconst RecentActivity = styled.div`\n  max-height: 300px;\n  overflow-y: auto;\n`;\n_c12 = RecentActivity;\nconst ActivityItem = styled.div`\n  display: flex;\n  justify-content: between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c13 = ActivityItem;\nconst ActivityInfo = styled.div`\n  flex: 1;\n`;\n_c14 = ActivityInfo;\nconst ActivityTitle = styled.div`\n  font-weight: 500;\n  margin-bottom: 4px;\n`;\n_c15 = ActivityTitle;\nconst ActivityDate = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n_c16 = ActivityDate;\nconst AgentReports = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('week');\n  const [stats, setStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvedTasks: 0,\n    rejectedTasks: 0,\n    averageTime: 0,\n    efficiency: 0\n  });\n  const [recentActivity, setRecentActivity] = useState([]);\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n\n      // In a real app, this would be a dedicated reports API endpoint\n      const [dashboardStats] = await Promise.all([apiService.getAgentDashboardStats()]);\n      setStats({\n        totalTasks: dashboardStats.totalAssigned || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        approvedTasks: dashboardStats.completedLeads || 0,\n        rejectedTasks: dashboardStats.rejectedLeads || 0,\n        averageTime: 2.5,\n        // Mock data\n        efficiency: 85 // Mock data\n      });\n\n      // Mock recent activity\n      setRecentActivity([{\n        id: 1,\n        title: 'Completed verification for John Doe',\n        date: '2024-01-16T14:30:00Z',\n        type: 'completed'\n      }, {\n        id: 2,\n        title: 'Started verification for Jane Smith',\n        date: '2024-01-16T10:15:00Z',\n        type: 'started'\n      }, {\n        id: 3,\n        title: 'Uploaded documents for Alice Johnson',\n        date: '2024-01-15T16:45:00Z',\n        type: 'upload'\n      }]);\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data\n      setStats({\n        totalTasks: 15,\n        completedTasks: 12,\n        approvedTasks: 10,\n        rejectedTasks: 2,\n        averageTime: 2.5,\n        efficiency: 85\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/agent/dashboard')\n  }, {\n    icon: '📋',\n    label: 'My Tasks',\n    onClick: () => navigate('/agent/tasks')\n  }, {\n    icon: '✅',\n    label: 'Completed',\n    onClick: () => navigate('/agent/completed')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    active: true\n  }];\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  const getCompletionRate = () => {\n    return stats.totalTasks > 0 ? Math.round(stats.completedTasks / stats.totalTasks * 100) : 0;\n  };\n  const getApprovalRate = () => {\n    return stats.completedTasks > 0 ? Math.round(stats.approvedTasks / stats.completedTasks * 100) : 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Performance Reports\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Performance Reports\",\n    navigationItems: navigationItems,\n    children: /*#__PURE__*/_jsxDEV(ReportsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: timeFilter,\n          onChange: e => setTimeFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"week\",\n            children: \"This Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"month\",\n            children: \"This Month\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"quarter\",\n            children: \"This Quarter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"year\",\n            children: \"This Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => window.print(),\n          children: \"\\uD83D\\uDCC4 Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.totalTasks\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Tasks Assigned\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.completedTasks\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Tasks Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.averageTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Avg. Days per Task\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: [stats.efficiency, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Efficiency Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n        children: [/*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Task Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PerformanceItem, {\n            children: [/*#__PURE__*/_jsxDEV(PerformanceLabel, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Completion Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [getCompletionRate(), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: getCompletionRate(),\n              color: \"#2e7d32\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PerformanceItem, {\n            children: [/*#__PURE__*/_jsxDEV(PerformanceLabel, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Approval Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [getApprovalRate(), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: getApprovalRate(),\n              color: \"#007E3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PerformanceItem, {\n            children: [/*#__PURE__*/_jsxDEV(PerformanceLabel, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Efficiency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [stats.efficiency, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(PerformanceBar, {\n              percentage: stats.efficiency,\n              color: \"#FFD100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Recent Activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RecentActivity, {\n            children: recentActivity.map(activity => /*#__PURE__*/_jsxDEV(ActivityItem, {\n              children: /*#__PURE__*/_jsxDEV(ActivityInfo, {\n                children: [/*#__PURE__*/_jsxDEV(ActivityTitle, {\n                  children: activity.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ActivityDate, {\n                  children: formatDate(activity.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)\n            }, activity.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Performance Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Tasks Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Total Assigned: \", stats.totalTasks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Completed: \", stats.completedTasks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Pending: \", stats.totalTasks - stats.completedTasks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Quality Metrics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Approved: \", stats.approvedTasks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Rejected: \", stats.rejectedTasks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Success Rate: \", getApprovalRate(), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Efficiency\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Avg. Time: \", stats.averageTime, \" days\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Efficiency: \", stats.efficiency, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Productivity: High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentReports, \"i92ButQ31c9JGAlLkJGwJqBi72g=\", false, function () {\n  return [useNavigate];\n});\n_c17 = AgentReports;\nexport default AgentReports;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"ReportsContainer\");\n$RefreshReg$(_c2, \"StatsGrid\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"StatValue\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"ChartContainer\");\n$RefreshReg$(_c7, \"ChartCard\");\n$RefreshReg$(_c8, \"ChartTitle\");\n$RefreshReg$(_c9, \"PerformanceBar\");\n$RefreshReg$(_c0, \"PerformanceItem\");\n$RefreshReg$(_c1, \"PerformanceLabel\");\n$RefreshReg$(_c10, \"FilterContainer\");\n$RefreshReg$(_c11, \"FilterSelect\");\n$RefreshReg$(_c12, \"RecentActivity\");\n$RefreshReg$(_c13, \"ActivityItem\");\n$RefreshReg$(_c14, \"ActivityInfo\");\n$RefreshReg$(_c15, \"ActivityTitle\");\n$RefreshReg$(_c16, \"ActivityDate\");\n$RefreshReg$(_c17, \"AgentReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "ReportsContainer", "div", "_c", "StatsGrid", "_c2", "StatCard", "_c3", "StatValue", "_c4", "StatLabel", "_c5", "ChartContainer", "_c6", "ChartCard", "_c7", "ChartTitle", "h3", "_c8", "PerformanceBar", "props", "percentage", "color", "_c9", "PerformanceItem", "_c0", "PerformanceLabel", "_c1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c10", "FilterSelect", "select", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "_c11", "RecentActivity", "_c12", "ActivityItem", "lightGray", "_c13", "ActivityInfo", "_c14", "ActivityTitle", "_c15", "ActivityDate", "textLight", "_c16", "AgentReports", "_s", "loading", "setLoading", "timeFilter", "setTimeFilter", "stats", "setStats", "totalTasks", "completedTasks", "approvedTasks", "rejectedTasks", "averageTime", "efficiency", "recentActivity", "setRecentActivity", "navigate", "loadReportsData", "dashboardStats", "Promise", "all", "getAgentDashboardStats", "totalAssigned", "completedLeads", "rejectedLeads", "id", "title", "date", "type", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "formatDate", "dateString", "Date", "toLocaleDateString", "getCompletionRate", "Math", "round", "getApprovalRate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "variant", "window", "print", "map", "activity", "style", "marginBottom", "display", "gridTemplateColumns", "gap", "_c17", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 32px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 14px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst PerformanceBar = styled.div<{ percentage: number; color: string }>`\n  width: 100%;\n  height: 20px;\n  background-color: #f0f0f0;\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 10px;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.percentage}%;\n    height: 100%;\n    background-color: ${props => props.color};\n    transition: width 0.3s ease;\n  }\n`;\n\nconst PerformanceItem = styled.div`\n  margin-bottom: 15px;\n`;\n\nconst PerformanceLabel = styled.div`\n  display: flex;\n  justify-content: between;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: 500;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst RecentActivity = styled.div`\n  max-height: 300px;\n  overflow-y: auto;\n`;\n\nconst ActivityItem = styled.div`\n  display: flex;\n  justify-content: between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst ActivityInfo = styled.div`\n  flex: 1;\n`;\n\nconst ActivityTitle = styled.div`\n  font-weight: 500;\n  margin-bottom: 4px;\n`;\n\nconst ActivityDate = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst AgentReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('week');\n  const [stats, setStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvedTasks: 0,\n    rejectedTasks: 0,\n    averageTime: 0,\n    efficiency: 0,\n  });\n  const [recentActivity, setRecentActivity] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      \n      // In a real app, this would be a dedicated reports API endpoint\n      const [dashboardStats] = await Promise.all([\n        apiService.getAgentDashboardStats(),\n      ]);\n\n      setStats({\n        totalTasks: dashboardStats.totalAssigned || 0,\n        completedTasks: dashboardStats.completedLeads || 0,\n        approvedTasks: dashboardStats.completedLeads || 0,\n        rejectedTasks: dashboardStats.rejectedLeads || 0,\n        averageTime: 2.5, // Mock data\n        efficiency: 85, // Mock data\n      });\n\n      // Mock recent activity\n      setRecentActivity([\n        {\n          id: 1,\n          title: 'Completed verification for John Doe',\n          date: '2024-01-16T14:30:00Z',\n          type: 'completed',\n        },\n        {\n          id: 2,\n          title: 'Started verification for Jane Smith',\n          date: '2024-01-16T10:15:00Z',\n          type: 'started',\n        },\n        {\n          id: 3,\n          title: 'Uploaded documents for Alice Johnson',\n          date: '2024-01-15T16:45:00Z',\n          type: 'upload',\n        },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data\n      setStats({\n        totalTasks: 15,\n        completedTasks: 12,\n        approvedTasks: 10,\n        rejectedTasks: 2,\n        averageTime: 2.5,\n        efficiency: 85,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },\n    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },\n    { icon: '📊', label: 'Reports', active: true },\n  ];\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getCompletionRate = () => {\n    return stats.totalTasks > 0 ? Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0;\n  };\n\n  const getApprovalRate = () => {\n    return stats.completedTasks > 0 ? Math.round((stats.approvedTasks / stats.completedTasks) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Performance Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Performance Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n          \n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n        </FilterContainer>\n\n        {/* Key Metrics */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{stats.totalTasks}</StatValue>\n            <StatLabel>Total Tasks Assigned</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.completedTasks}</StatValue>\n            <StatLabel>Tasks Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.averageTime}</StatValue>\n            <StatLabel>Avg. Days per Task</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{stats.efficiency}%</StatValue>\n            <StatLabel>Efficiency Score</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Performance Charts */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Task Performance</ChartTitle>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Completion Rate</span>\n                <span>{getCompletionRate()}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={getCompletionRate()} color=\"#2e7d32\" />\n            </PerformanceItem>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Approval Rate</span>\n                <span>{getApprovalRate()}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={getApprovalRate()} color=\"#007E3A\" />\n            </PerformanceItem>\n            <PerformanceItem>\n              <PerformanceLabel>\n                <span>Efficiency</span>\n                <span>{stats.efficiency}%</span>\n              </PerformanceLabel>\n              <PerformanceBar percentage={stats.efficiency} color=\"#FFD100\" />\n            </PerformanceItem>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>Recent Activity</ChartTitle>\n            <RecentActivity>\n              {recentActivity.map((activity) => (\n                <ActivityItem key={activity.id}>\n                  <ActivityInfo>\n                    <ActivityTitle>{activity.title}</ActivityTitle>\n                    <ActivityDate>{formatDate(activity.date)}</ActivityDate>\n                  </ActivityInfo>\n                </ActivityItem>\n              ))}\n            </RecentActivity>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Summary Card */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Performance Summary</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4>Tasks Overview</h4>\n              <p>Total Assigned: {stats.totalTasks}</p>\n              <p>Completed: {stats.completedTasks}</p>\n              <p>Pending: {stats.totalTasks - stats.completedTasks}</p>\n            </div>\n            <div>\n              <h4>Quality Metrics</h4>\n              <p>Approved: {stats.approvedTasks}</p>\n              <p>Rejected: {stats.rejectedTasks}</p>\n              <p>Success Rate: {getApprovalRate()}%</p>\n            </div>\n            <div>\n              <h4>Efficiency</h4>\n              <p>Avg. Time: {stats.averageTime} days</p>\n              <p>Efficiency: {stats.efficiency}%</p>\n              <p>Productivity: High</p>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAGR,MAAM,CAACS,GAAG;AACnC;AACA;AACA,CAAC;AAACC,EAAA,GAHIF,gBAAgB;AAKtB,MAAMG,SAAS,GAAGX,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGb,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GARID,QAAQ;AAUd,MAAME,SAAS,GAAGf,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGjB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAJID,SAAS;AAMf,MAAME,cAAc,GAAGnB,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GATID,cAAc;AAWpB,MAAME,SAAS,GAAGrB,MAAM,CAACE,IAAI,CAAC;AAC9B;AACA,CAAC;AAACoB,GAAA,GAFID,SAAS;AAIf,MAAME,UAAU,GAAGvB,MAAM,CAACwB,EAAE;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,cAAc,GAAG1B,MAAM,CAACS,GAA0C;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAakB,KAAK,IAAIA,KAAK,CAACC,UAAU;AACtC;AACA,wBAAwBD,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC5C;AACA;AACA,CAAC;AAACC,GAAA,GAhBIJ,cAAc;AAkBpB,MAAMK,eAAe,GAAG/B,MAAM,CAACS,GAAG;AAClC;AACA,CAAC;AAACuB,GAAA,GAFID,eAAe;AAIrB,MAAME,gBAAgB,GAAGjC,MAAM,CAACS,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GANID,gBAAgB;AAQtB,MAAME,eAAe,GAAGnC,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAGrC,MAAM,CAACsC,MAAM;AAClC;AACA,sBAAsBX,KAAK,IAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBd,KAAK,IAAIA,KAAK,CAACY,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBhB,KAAK,IAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACC,IAAA,GAXIR,YAAY;AAalB,MAAMS,cAAc,GAAG9C,MAAM,CAACS,GAAG;AACjC;AACA;AACA,CAAC;AAACsC,IAAA,GAHID,cAAc;AAKpB,MAAME,YAAY,GAAGhD,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA,6BAA6BkB,KAAK,IAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACS,SAAS;AAClE;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,YAAY;AAYlB,MAAMG,YAAY,GAAGnD,MAAM,CAACS,GAAG;AAC/B;AACA,CAAC;AAAC2C,IAAA,GAFID,YAAY;AAIlB,MAAME,aAAa,GAAGrD,MAAM,CAACS,GAAG;AAChC;AACA;AACA,CAAC;AAAC6C,IAAA,GAHID,aAAa;AAKnB,MAAME,YAAY,GAAGvD,MAAM,CAACS,GAAG;AAC/B;AACA,WAAWkB,KAAK,IAAIA,KAAK,CAACY,KAAK,CAACC,MAAM,CAACgB,SAAS;AAChD,CAAC;AAACC,IAAA,GAHIF,YAAY;AAKlB,MAAMG,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,MAAM,CAAC;EACpD,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC;IACjCqE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM6E,QAAQ,GAAG3E,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd6E,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;EAEhB,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACe,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzCzE,UAAU,CAAC0E,sBAAsB,CAAC,CAAC,CACpC,CAAC;MAEFd,QAAQ,CAAC;QACPC,UAAU,EAAEU,cAAc,CAACI,aAAa,IAAI,CAAC;QAC7Cb,cAAc,EAAES,cAAc,CAACK,cAAc,IAAI,CAAC;QAClDb,aAAa,EAAEQ,cAAc,CAACK,cAAc,IAAI,CAAC;QACjDZ,aAAa,EAAEO,cAAc,CAACM,aAAa,IAAI,CAAC;QAChDZ,WAAW,EAAE,GAAG;QAAE;QAClBC,UAAU,EAAE,EAAE,CAAE;MAClB,CAAC,CAAC;;MAEF;MACAE,iBAAiB,CAAC,CAChB;QACEU,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,qCAAqC;QAC5CC,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE;MACR,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,qCAAqC;QAC5CC,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE;MACR,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,sCAAsC;QAC7CC,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE;MACR,CAAC,CACF,CAAC;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACAtB,QAAQ,CAAC;QACPC,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,CAAC;QAChBC,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC/E;IAAEgB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,cAAc;EAAE,CAAC,EAC1E;IAAEgB,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAMlB,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC9E;IAAEgB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEE,MAAM,EAAE;EAAK,CAAC,CAC/C;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOlC,KAAK,CAACE,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAEpC,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;EAC/F,CAAC;EAED,MAAMmC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOrC,KAAK,CAACG,cAAc,GAAG,CAAC,GAAGgC,IAAI,CAACC,KAAK,CAAEpC,KAAK,CAACI,aAAa,GAAGJ,KAAK,CAACG,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;EACtG,CAAC;EAED,IAAIP,OAAO,EAAE;IACX,oBACErD,OAAA,CAACN,eAAe;MAACmF,KAAK,EAAC,qBAAqB;MAACK,eAAe,EAAEA,eAAgB;MAAAa,QAAA,eAC5E/F,OAAA,CAACH,cAAc;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACEnG,OAAA,CAACN,eAAe;IAACmF,KAAK,EAAC,qBAAqB;IAACK,eAAe,EAAEA,eAAgB;IAAAa,QAAA,eAC5E/F,OAAA,CAACC,gBAAgB;MAAA8F,QAAA,gBACf/F,OAAA,CAAC4B,eAAe;QAAAmE,QAAA,gBACd/F,OAAA,CAAC8B,YAAY;UAACsE,KAAK,EAAE7C,UAAW;UAAC8C,QAAQ,EAAGC,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAL,QAAA,gBAC9E/F,OAAA;YAAQoG,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvCnG,OAAA;YAAQoG,KAAK,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzCnG,OAAA;YAAQoG,KAAK,EAAC,SAAS;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7CnG,OAAA;YAAQoG,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEfnG,OAAA,CAACJ,MAAM;UAAC4G,OAAO,EAAC,SAAS;UAACnB,OAAO,EAAEA,CAAA,KAAMoB,MAAM,CAACC,KAAK,CAAC,CAAE;UAAAX,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGlBnG,OAAA,CAACI,SAAS;QAAA2F,QAAA,gBACR/F,OAAA,CAACM,QAAQ;UAAAyF,QAAA,gBACP/F,OAAA,CAACQ,SAAS;YAAAuF,QAAA,EAAEtC,KAAK,CAACE;UAAU;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzCnG,OAAA,CAACU,SAAS;YAAAqF,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACXnG,OAAA,CAACM,QAAQ;UAAAyF,QAAA,gBACP/F,OAAA,CAACQ,SAAS;YAAAuF,QAAA,EAAEtC,KAAK,CAACG;UAAc;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7CnG,OAAA,CAACU,SAAS;YAAAqF,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACXnG,OAAA,CAACM,QAAQ;UAAAyF,QAAA,gBACP/F,OAAA,CAACQ,SAAS;YAAAuF,QAAA,EAAEtC,KAAK,CAACM;UAAW;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1CnG,OAAA,CAACU,SAAS;YAAAqF,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXnG,OAAA,CAACM,QAAQ;UAAAyF,QAAA,gBACP/F,OAAA,CAACQ,SAAS;YAAAuF,QAAA,GAAEtC,KAAK,CAACO,UAAU,EAAC,GAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1CnG,OAAA,CAACU,SAAS;YAAAqF,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGZnG,OAAA,CAACY,cAAc;QAAAmF,QAAA,gBACb/F,OAAA,CAACc,SAAS;UAAAiF,QAAA,gBACR/F,OAAA,CAACgB,UAAU;YAAA+E,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzCnG,OAAA,CAACwB,eAAe;YAAAuE,QAAA,gBACd/F,OAAA,CAAC0B,gBAAgB;cAAAqE,QAAA,gBACf/F,OAAA;gBAAA+F,QAAA,EAAM;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BnG,OAAA;gBAAA+F,QAAA,GAAOJ,iBAAiB,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACnBnG,OAAA,CAACmB,cAAc;cAACE,UAAU,EAAEsE,iBAAiB,CAAC,CAAE;cAACrE,KAAK,EAAC;YAAS;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAClBnG,OAAA,CAACwB,eAAe;YAAAuE,QAAA,gBACd/F,OAAA,CAAC0B,gBAAgB;cAAAqE,QAAA,gBACf/F,OAAA;gBAAA+F,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BnG,OAAA;gBAAA+F,QAAA,GAAOD,eAAe,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACnBnG,OAAA,CAACmB,cAAc;cAACE,UAAU,EAAEyE,eAAe,CAAC,CAAE;cAACxE,KAAK,EAAC;YAAS;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAClBnG,OAAA,CAACwB,eAAe;YAAAuE,QAAA,gBACd/F,OAAA,CAAC0B,gBAAgB;cAAAqE,QAAA,gBACf/F,OAAA;gBAAA+F,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBnG,OAAA;gBAAA+F,QAAA,GAAOtC,KAAK,CAACO,UAAU,EAAC,GAAC;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACnBnG,OAAA,CAACmB,cAAc;cAACE,UAAU,EAAEoC,KAAK,CAACO,UAAW;cAAC1C,KAAK,EAAC;YAAS;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEZnG,OAAA,CAACc,SAAS;UAAAiF,QAAA,gBACR/F,OAAA,CAACgB,UAAU;YAAA+E,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxCnG,OAAA,CAACuC,cAAc;YAAAwD,QAAA,EACZ9B,cAAc,CAAC0C,GAAG,CAAEC,QAAQ,iBAC3B5G,OAAA,CAACyC,YAAY;cAAAsD,QAAA,eACX/F,OAAA,CAAC4C,YAAY;gBAAAmD,QAAA,gBACX/F,OAAA,CAAC8C,aAAa;kBAAAiD,QAAA,EAAEa,QAAQ,CAAC/B;gBAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CAAC,eAC/CnG,OAAA,CAACgD,YAAY;kBAAA+C,QAAA,EAAER,UAAU,CAACqB,QAAQ,CAAC9B,IAAI;gBAAC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC,GAJES,QAAQ,CAAChC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKhB,CACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGjBnG,OAAA,CAACL,IAAI;QAAAoG,QAAA,gBACH/F,OAAA;UAAI6G,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAExF,KAAK,EAAE;UAAU,CAAE;UAAAyE,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EnG,OAAA;UAAK6G,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAlB,QAAA,gBACxG/F,OAAA;YAAA+F,QAAA,gBACE/F,OAAA;cAAA+F,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBnG,OAAA;cAAA+F,QAAA,GAAG,kBAAgB,EAACtC,KAAK,CAACE,UAAU;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCnG,OAAA;cAAA+F,QAAA,GAAG,aAAW,EAACtC,KAAK,CAACG,cAAc;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxCnG,OAAA;cAAA+F,QAAA,GAAG,WAAS,EAACtC,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACG,cAAc;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNnG,OAAA;YAAA+F,QAAA,gBACE/F,OAAA;cAAA+F,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBnG,OAAA;cAAA+F,QAAA,GAAG,YAAU,EAACtC,KAAK,CAACI,aAAa;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCnG,OAAA;cAAA+F,QAAA,GAAG,YAAU,EAACtC,KAAK,CAACK,aAAa;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCnG,OAAA;cAAA+F,QAAA,GAAG,gBAAc,EAACD,eAAe,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNnG,OAAA;YAAA+F,QAAA,gBACE/F,OAAA;cAAA+F,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnG,OAAA;cAAA+F,QAAA,GAAG,aAAW,EAACtC,KAAK,CAACM,WAAW,EAAC,OAAK;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1CnG,OAAA;cAAA+F,QAAA,GAAG,cAAY,EAACtC,KAAK,CAACO,UAAU,EAAC,GAAC;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtCnG,OAAA;cAAA+F,QAAA,EAAG;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAAC/C,EAAA,CA9MID,YAAsB;EAAA,QAYT3D,WAAW;AAAA;AAAA0H,IAAA,GAZxB/D,YAAsB;AAgN5B,eAAeA,YAAY;AAAC,IAAAhD,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAS,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAgE,IAAA;AAAAC,YAAA,CAAAhH,EAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}