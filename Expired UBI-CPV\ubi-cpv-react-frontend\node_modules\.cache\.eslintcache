[{"D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx": "1", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts": "2", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx": "3", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts": "4", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx": "5", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx": "6", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx": "7", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx": "8", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx": "9", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx": "10", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "11", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx": "12", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx": "13", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts": "14", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx": "15", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx": "16", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx": "17", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx": "18", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx": "19", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx": "20", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx": "21", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx": "22", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx": "23", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx": "24", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx": "25", "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx": "26"}, {"size": 554, "mtime": 1748411605451, "results": "27", "hashOfConfig": "28"}, {"size": 425, "mtime": 1748411601005, "results": "29", "hashOfConfig": "28"}, {"size": 5883, "mtime": 1748528044635, "results": "30", "hashOfConfig": "28"}, {"size": 6669, "mtime": 1748411913906, "results": "31", "hashOfConfig": "28"}, {"size": 3146, "mtime": 1748415256095, "results": "32", "hashOfConfig": "28"}, {"size": 9560, "mtime": 1748415614108, "results": "33", "hashOfConfig": "28"}, {"size": 6657, "mtime": 1748411994656, "results": "34", "hashOfConfig": "28"}, {"size": 10959, "mtime": 1748415820206, "results": "35", "hashOfConfig": "28"}, {"size": 10248, "mtime": 1748415743596, "results": "36", "hashOfConfig": "28"}, {"size": 11411, "mtime": 1748528557126, "results": "37", "hashOfConfig": "28"}, {"size": 1503, "mtime": 1748412009127, "results": "38", "hashOfConfig": "28"}, {"size": 11683, "mtime": 1748413757670, "results": "39", "hashOfConfig": "28"}, {"size": 14073, "mtime": 1748417381880, "results": "40", "hashOfConfig": "28"}, {"size": 16192, "mtime": 1748415139402, "results": "41", "hashOfConfig": "28"}, {"size": 6505, "mtime": 1748412041801, "results": "42", "hashOfConfig": "28"}, {"size": 11264, "mtime": 1748417320847, "results": "43", "hashOfConfig": "28"}, {"size": 6033, "mtime": 1748413518514, "results": "44", "hashOfConfig": "28"}, {"size": 16782, "mtime": 1748413825867, "results": "45", "hashOfConfig": "28"}, {"size": 9959, "mtime": 1748528093222, "results": "46", "hashOfConfig": "28"}, {"size": 10782, "mtime": 1748528140885, "results": "47", "hashOfConfig": "28"}, {"size": 9782, "mtime": 1748528186911, "results": "48", "hashOfConfig": "28"}, {"size": 15167, "mtime": 1748528252181, "results": "49", "hashOfConfig": "28"}, {"size": 12786, "mtime": 1748528312980, "results": "50", "hashOfConfig": "28"}, {"size": 14420, "mtime": 1748528379281, "results": "51", "hashOfConfig": "28"}, {"size": 15920, "mtime": 1748528446243, "results": "52", "hashOfConfig": "28"}, {"size": 14710, "mtime": 1748528507904, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkgvnm", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\index.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\reportWebVitals.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\App.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\styles\\GlobalStyles.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AgentDashboard.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\Login.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\AdminDashboard.tsx", ["132"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Dashboard\\SupervisorDashboard.tsx", ["133", "134"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadDetails.tsx", ["135"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Documents\\DocumentUpload.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Verification\\VerificationForm.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\services\\apiService.ts", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Layout\\DashboardLayout.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\CreateLead.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Camera\\CameraCapture.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\UserManagement.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentTasks.tsx", ["136"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentCompleted.tsx", ["137"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Agent\\AgentReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReview.tsx", ["138"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Supervisor\\SupervisorReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Admin\\AdminReports.tsx", [], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Leads\\LeadsList.tsx", ["139"], [], "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\ubi-cpv-react-frontend\\src\\components\\Profile\\UserProfile.tsx", ["140"], [], {"ruleId": "141", "severity": 1, "message": "142", "line": 5, "column": 24, "nodeType": "143", "messageId": "144", "endLine": 5, "endColumn": 38}, {"ruleId": "141", "severity": 1, "message": "142", "line": 5, "column": 24, "nodeType": "143", "messageId": "144", "endLine": 5, "endColumn": 38}, {"ruleId": "145", "severity": 1, "message": "146", "line": 141, "column": 6, "nodeType": "147", "endLine": 141, "endColumn": 20, "suggestions": "148"}, {"ruleId": "141", "severity": 1, "message": "149", "line": 217, "column": 9, "nodeType": "143", "messageId": "144", "endLine": 217, "endColumn": 25}, {"ruleId": "145", "severity": 1, "message": "150", "line": 147, "column": 6, "nodeType": "147", "endLine": 147, "endColumn": 20, "suggestions": "151"}, {"ruleId": "145", "severity": 1, "message": "152", "line": 149, "column": 6, "nodeType": "147", "endLine": 149, "endColumn": 20, "suggestions": "153"}, {"ruleId": "145", "severity": 1, "message": "154", "line": 206, "column": 6, "nodeType": "147", "endLine": 206, "endColumn": 20, "suggestions": "155"}, {"ruleId": "145", "severity": 1, "message": "156", "line": 207, "column": 6, "nodeType": "147", "endLine": 207, "endColumn": 59, "suggestions": "157"}, {"ruleId": "145", "severity": 1, "message": "158", "line": 185, "column": 6, "nodeType": "147", "endLine": 185, "endColumn": 8, "suggestions": "159"}, "@typescript-eslint/no-unused-vars", "'LoadingSpinner' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["160"], "'handleAssignLead' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["161"], "React Hook useEffect has a missing dependency: 'loadCompletedTasks'. Either include it or remove the dependency array.", ["162"], "React Hook useEffect has a missing dependency: 'loadReviewTasks'. Either include it or remove the dependency array.", ["163"], "React Hook useEffect has a missing dependency: 'loadLeads'. Either include it or remove the dependency array.", ["164"], "React Hook useEffect has missing dependencies: 'loadUserProfile' and 'loadUserStats'. Either include them or remove the dependency array.", ["165"], {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, {"desc": "172", "fix": "173"}, {"desc": "174", "fix": "175"}, {"desc": "176", "fix": "177"}, "Update the dependencies array to be: [loadDashboardData, statusFilter]", {"range": "178", "text": "179"}, "Update the dependencies array to be: [loadTasks, statusFilter]", {"range": "180", "text": "181"}, "Update the dependencies array to be: [loadCompletedTasks, statusFilter]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [loadReviewTasks, statusFilter]", {"range": "184", "text": "185"}, "Update the dependencies array to be: [currentPage, statusFilter, sortField, sortDirection, loadLeads]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [loadUserProfile, loadUserStats]", {"range": "188", "text": "189"}, [3471, 3485], "[loadDashboardData, statusFilter]", [3378, 3392], "[loadTasks, statusFilter]", [3512, 3526], "[loadCompletedTasks, statusFilter]", [4811, 4825], "[loadReviewTasks, statusFilter]", [5059, 5112], "[currentPage, statusFilter, sortField, sortDirection, loadLeads]", [4286, 4288], "[loadUserProfile, loadUserStats]"]