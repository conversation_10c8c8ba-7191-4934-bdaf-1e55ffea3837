{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Supervisor\\\\SupervisorReview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c2 = FilterSelect;\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c3 = SearchInput;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c4 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c5 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c7 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c8 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.status) {\n    case 'pending-review':\n      return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n    case 'approved':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    case 'rejected':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c9 = StatusBadge;\nconst PriorityBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.priority) {\n    case 'high':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    case 'medium':\n      return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n    case 'low':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c0 = PriorityBadge;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n_c1 = ActionButtons;\nconst ReviewModal = styled.div`\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n`;\n_c10 = ReviewModal;\nconst ModalContent = styled.div`\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow-y: auto;\n`;\n_c11 = ModalContent;\nconst ModalTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n`;\n_c12 = ModalTitle;\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c13 = TextArea;\nconst ModalActions = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n_c14 = ModalActions;\nconst SupervisorReview = () => {\n  _s();\n  const [reviewTasks, setReviewTasks] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLead, setSelectedLead] = useState(null);\n  const [reviewAction, setReviewAction] = useState(null);\n  const [reviewComments, setReviewComments] = useState('');\n  const [rejectionReason, setRejectionReason] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadReviewTasks();\n  }, [statusFilter]);\n  const loadReviewTasks = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeads(1, 100, statusFilter === 'all' ? undefined : statusFilter);\n      setReviewTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading review tasks:', error);\n      // Mock data for demo\n      setReviewTasks([{\n        leadId: 5,\n        customerName: 'Charlie Brown',\n        mobileNumber: '9876543214',\n        loanType: 'Home Loan',\n        status: 'pending-review',\n        createdDate: '2024-01-12T09:00:00Z',\n        assignedDate: '2024-01-12T10:00:00Z',\n        submittedDate: '2024-01-15T17:30:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Agent Smith',\n        documentCount: 4,\n        croppedImageCount: 3\n      }, {\n        leadId: 6,\n        customerName: 'Diana Prince',\n        mobileNumber: '9876543215',\n        loanType: 'Car Loan',\n        status: 'pending-review',\n        createdDate: '2024-01-13T11:15:00Z',\n        assignedDate: '2024-01-13T12:00:00Z',\n        submittedDate: '2024-01-16T09:45:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Agent Johnson',\n        documentCount: 3,\n        croppedImageCount: 2\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredTasks = reviewTasks.filter(task => task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || task.mobileNumber.includes(searchTerm) || task.loanType.toLowerCase().includes(searchTerm.toLowerCase()) || task.assignedToName && task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()));\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/supervisor/dashboard')\n  }, {\n    icon: '👁️',\n    label: 'Review Queue',\n    active: true\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    onClick: () => navigate('/supervisor/reports')\n  }, {\n    icon: '👥',\n    label: 'Team',\n    onClick: () => navigate('/supervisor/team')\n  }];\n  const handleReviewAction = (lead, action) => {\n    setSelectedLead(lead);\n    setReviewAction(action);\n    setReviewComments('');\n    setRejectionReason('');\n  };\n  const submitReview = async () => {\n    if (!selectedLead || !reviewAction) return;\n    try {\n      const newStatus = reviewAction === 'approve' ? 'approved' : 'rejected';\n      await apiService.updateLeadStatus(selectedLead.leadId, newStatus, reviewComments, reviewAction === 'reject' ? rejectionReason : undefined);\n\n      // Update local state\n      setReviewTasks(tasks => tasks.map(task => task.leadId === selectedLead.leadId ? {\n        ...task,\n        status: newStatus\n      } : task));\n\n      // Close modal\n      setSelectedLead(null);\n      setReviewAction(null);\n\n      // Reload if filtering by status\n      if (statusFilter !== 'all') {\n        loadReviewTasks();\n      }\n    } catch (error) {\n      console.error('Error submitting review:', error);\n      alert('Failed to submit review');\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  const getPriority = submittedDate => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 2) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n  const calculateWaitTime = submittedDate => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    return `${daysDiff} day${daysDiff !== 1 ? 's' : ''}`;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"Review Queue\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"Review Queue\",\n    navigationItems: navigationItems,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '20px',\n          color: '#007E3A'\n        },\n        children: \"Pending Reviews\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: statusFilter,\n          onChange: e => setStatusFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending-review\",\n            children: \"Pending Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"approved\",\n            children: \"Approved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"rejected\",\n            children: \"Rejected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchInput, {\n          type: \"text\",\n          placeholder: \"Search by customer, mobile, loan type, or agent...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Mobile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Agent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Priority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Submitted\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Wait Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredTasks.map(task => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.mobileNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.loanType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.assignedToName || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  status: task.status,\n                  children: task.status.replace('-', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.submittedDate && /*#__PURE__*/_jsxDEV(PriorityBadge, {\n                  priority: getPriority(task.submittedDate),\n                  children: getPriority(task.submittedDate).toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.submittedDate ? formatDate(task.submittedDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.submittedDate ? calculateWaitTime(task.submittedDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(ActionButtons, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    onClick: () => navigate(`/lead/${task.leadId}`),\n                    children: \"View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), task.status === 'pending-review' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"secondary\",\n                      onClick: () => handleReviewAction(task, 'approve'),\n                      children: \"Approve\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"danger\",\n                      onClick: () => handleReviewAction(task, 'reject'),\n                      children: \"Reject\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this)]\n            }, task.leadId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), filteredTasks.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: searchTerm ? 'No tasks found matching your search.' : 'No tasks pending review.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReviewModal, {\n      isOpen: !!selectedLead && !!reviewAction,\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        children: [/*#__PURE__*/_jsxDEV(ModalTitle, {\n          children: [reviewAction === 'approve' ? 'Approve' : 'Reject', \" Lead - \", selectedLead === null || selectedLead === void 0 ? void 0 : selectedLead.customerName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '5px',\n              fontWeight: '500'\n            },\n            children: \"Comments:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n            value: reviewComments,\n            onChange: e => setReviewComments(e.target.value),\n            placeholder: \"Add your review comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), reviewAction === 'reject' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '5px',\n              fontWeight: '500'\n            },\n            children: \"Rejection Reason:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FilterSelect, {\n            value: rejectionReason,\n            onChange: e => setRejectionReason(e.target.value),\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select reason...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"incomplete-documents\",\n              children: \"Incomplete Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"poor-quality\",\n              children: \"Poor Quality Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"verification-failed\",\n              children: \"Verification Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"incorrect-information\",\n              children: \"Incorrect Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"other\",\n              children: \"Other\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ModalActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: () => {\n              setSelectedLead(null);\n              setReviewAction(null);\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: reviewAction === 'approve' ? 'secondary' : 'danger',\n            onClick: submitReview,\n            disabled: !reviewComments || reviewAction === 'reject' && !rejectionReason,\n            children: reviewAction === 'approve' ? 'Approve' : 'Reject'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this);\n};\n_s(SupervisorReview, \"qX7USpZtQEiF5SqapExXZstEvM0=\", false, function () {\n  return [useNavigate];\n});\n_c15 = SupervisorReview;\nexport default SupervisorReview;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"FilterContainer\");\n$RefreshReg$(_c2, \"FilterSelect\");\n$RefreshReg$(_c3, \"SearchInput\");\n$RefreshReg$(_c4, \"TableContainer\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"TableRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"PriorityBadge\");\n$RefreshReg$(_c1, \"ActionButtons\");\n$RefreshReg$(_c10, \"ReviewModal\");\n$RefreshReg$(_c11, \"ModalContent\");\n$RefreshReg$(_c12, \"ModalTitle\");\n$RefreshReg$(_c13, \"TextArea\");\n$RefreshReg$(_c14, \"ModalActions\");\n$RefreshReg$(_c15, \"SupervisorReview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "FilterSelect", "select", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "_c2", "SearchInput", "input", "_c3", "TableContainer", "_c4", "Table", "table", "_c5", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c6", "TableCell", "td", "_c7", "TableRow", "tr", "_c8", "StatusBadge", "span", "status", "_c9", "PriorityBadge", "priority", "_c0", "ActionButtons", "_c1", "ReviewModal", "isOpen", "_c10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c11", "ModalTitle", "h3", "_c12", "TextArea", "textarea", "_c13", "ModalActions", "_c14", "SupervisorReview", "_s", "reviewTasks", "setReviewTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>", "setSelectedLead", "reviewAction", "setReviewAction", "reviewComments", "setReviewComments", "rejectionReason", "setRejectionReason", "navigate", "loadReviewTasks", "response", "getLeads", "undefined", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "submittedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "filter", "task", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "handleReviewAction", "lead", "action", "submitReview", "newStatus", "updateLeadStatus", "tasks", "map", "alert", "formatDate", "dateString", "Date", "toLocaleDateString", "getPriority", "daysDiff", "Math", "floor", "now", "getTime", "calculateWaitTime", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "color", "value", "onChange", "e", "target", "type", "placeholder", "replace", "toUpperCase", "size", "variant", "length", "textAlign", "padding", "display", "fontWeight", "width", "disabled", "_c15", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Supervisor/SupervisorReview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'pending-review':\n        return `\n          background-color: #f3e5f5;\n          color: #4a148c;\n        `;\n      case 'approved':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      case 'rejected':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst PriorityBadge = styled.span<{ priority: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.priority) {\n      case 'high':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'medium':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'low':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst ReviewModal = styled.div<{ isOpen: boolean }>`\n  display: ${props => props.isOpen ? 'flex' : 'none'};\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n`;\n\nconst ModalContent = styled.div`\n  background: white;\n  padding: 30px;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 80vh;\n  overflow-y: auto;\n`;\n\nconst ModalTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: 10px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst ModalActions = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst SupervisorReview: React.FC = () => {\n  const [reviewTasks, setReviewTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('pending-review');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLead, setSelectedLead] = useState<LeadListItem | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);\n  const [reviewComments, setReviewComments] = useState('');\n  const [rejectionReason, setRejectionReason] = useState('');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReviewTasks();\n  }, [statusFilter]);\n\n  const loadReviewTasks = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeads(1, 100, statusFilter === 'all' ? undefined : statusFilter);\n      setReviewTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading review tasks:', error);\n      // Mock data for demo\n      setReviewTasks([\n        {\n          leadId: 5,\n          customerName: 'Charlie Brown',\n          mobileNumber: '9876543214',\n          loanType: 'Home Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-12T09:00:00Z',\n          assignedDate: '2024-01-12T10:00:00Z',\n          submittedDate: '2024-01-15T17:30:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Smith',\n          documentCount: 4,\n          croppedImageCount: 3,\n        },\n        {\n          leadId: 6,\n          customerName: 'Diana Prince',\n          mobileNumber: '9876543215',\n          loanType: 'Car Loan',\n          status: 'pending-review',\n          createdDate: '2024-01-13T11:15:00Z',\n          assignedDate: '2024-01-13T12:00:00Z',\n          submittedDate: '2024-01-16T09:45:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Agent Johnson',\n          documentCount: 3,\n          croppedImageCount: 2,\n        },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = reviewTasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (task.assignedToName && task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },\n    { icon: '👁️', label: 'Review Queue', active: true },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },\n    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },\n  ];\n\n  const handleReviewAction = (lead: LeadListItem, action: 'approve' | 'reject') => {\n    setSelectedLead(lead);\n    setReviewAction(action);\n    setReviewComments('');\n    setRejectionReason('');\n  };\n\n  const submitReview = async () => {\n    if (!selectedLead || !reviewAction) return;\n\n    try {\n      const newStatus = reviewAction === 'approve' ? 'approved' : 'rejected';\n      await apiService.updateLeadStatus(\n        selectedLead.leadId,\n        newStatus,\n        reviewComments,\n        reviewAction === 'reject' ? rejectionReason : undefined\n      );\n\n      // Update local state\n      setReviewTasks(tasks => \n        tasks.map(task => \n          task.leadId === selectedLead.leadId \n            ? { ...task, status: newStatus }\n            : task\n        )\n      );\n\n      // Close modal\n      setSelectedLead(null);\n      setReviewAction(null);\n      \n      // Reload if filtering by status\n      if (statusFilter !== 'all') {\n        loadReviewTasks();\n      }\n\n    } catch (error) {\n      console.error('Error submitting review:', error);\n      alert('Failed to submit review');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getPriority = (submittedDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 2) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n\n  const calculateWaitTime = (submittedDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));\n    return `${daysDiff} day${daysDiff !== 1 ? 's' : ''}`;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Review Queue\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"Review Queue\" navigationItems={navigationItems}>\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Pending Reviews</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"pending-review\">Pending Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"all\">All Reviews</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer, mobile, loan type, or agent...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Agent</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Priority</TableHeader>\n                <TableHeader>Submitted</TableHeader>\n                <TableHeader>Wait Time</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>{task.assignedToName || '-'}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate && (\n                      <PriorityBadge priority={getPriority(task.submittedDate)}>\n                        {getPriority(task.submittedDate).toUpperCase()}\n                      </PriorityBadge>\n                    )}\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    {task.submittedDate ? calculateWaitTime(task.submittedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    <ActionButtons>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => navigate(`/lead/${task.leadId}`)}\n                      >\n                        View\n                      </Button>\n                      {task.status === 'pending-review' && (\n                        <>\n                          <Button\n                            size=\"sm\"\n                            variant=\"secondary\"\n                            onClick={() => handleReviewAction(task, 'approve')}\n                          >\n                            Approve\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"danger\"\n                            onClick={() => handleReviewAction(task, 'reject')}\n                          >\n                            Reject\n                          </Button>\n                        </>\n                      )}\n                    </ActionButtons>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No tasks found matching your search.' : 'No tasks pending review.'}\n          </div>\n        )}\n      </Card>\n\n      {/* Review Modal */}\n      <ReviewModal isOpen={!!selectedLead && !!reviewAction}>\n        <ModalContent>\n          <ModalTitle>\n            {reviewAction === 'approve' ? 'Approve' : 'Reject'} Lead - {selectedLead?.customerName}\n          </ModalTitle>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>\n              Comments:\n            </label>\n            <TextArea\n              value={reviewComments}\n              onChange={(e) => setReviewComments(e.target.value)}\n              placeholder=\"Add your review comments...\"\n            />\n          </div>\n\n          {reviewAction === 'reject' && (\n            <div style={{ marginBottom: '15px' }}>\n              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>\n                Rejection Reason:\n              </label>\n              <FilterSelect\n                value={rejectionReason}\n                onChange={(e) => setRejectionReason(e.target.value)}\n                style={{ width: '100%' }}\n              >\n                <option value=\"\">Select reason...</option>\n                <option value=\"incomplete-documents\">Incomplete Documents</option>\n                <option value=\"poor-quality\">Poor Quality Images</option>\n                <option value=\"verification-failed\">Verification Failed</option>\n                <option value=\"incorrect-information\">Incorrect Information</option>\n                <option value=\"other\">Other</option>\n              </FilterSelect>\n            </div>\n          )}\n\n          <ModalActions>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setSelectedLead(null);\n                setReviewAction(null);\n              }}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant={reviewAction === 'approve' ? 'secondary' : 'danger'}\n              onClick={submitReview}\n              disabled={!reviewComments || (reviewAction === 'reject' && !rejectionReason)}\n            >\n              {reviewAction === 'approve' ? 'Approve' : 'Reject'}\n            </Button>\n          </ModalActions>\n        </ModalContent>\n      </ReviewModal>\n    </DashboardLayout>\n  );\n};\n\nexport default SupervisorReview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAsB,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,eAAe,GAAGV,MAAM,CAACW,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,YAAY,GAAGb,MAAM,CAACc,MAAM;AAClC;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACC,GAAA,GAXIT,YAAY;AAalB,MAAMU,WAAW,GAAGvB,MAAM,CAACwB,KAAK;AAChC;AACA;AACA;AACA,sBAAsBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACI,GAAA,GAZIF,WAAW;AAcjB,MAAMG,cAAc,GAAG1B,MAAM,CAACW,GAAG;AACjC;AACA,CAAC;AAACgB,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG5B,MAAM,CAAC6B,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAG/B,MAAM,CAACgC,EAAE;AAC7B;AACA;AACA,6BAA6BjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAClE,sBAAsBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,QAAQ;AAC1D;AACA,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGrC,MAAM,CAACsC,EAAE;AAC3B;AACA;AACA,6BAA6BvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGxC,MAAM,CAACyC,EAAE;AAC1B;AACA,wBAAwB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG3C,MAAM,CAAC4C,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI7B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC8B,MAAM;IAClB,KAAK,gBAAgB;MACnB,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIH,WAAW;AAiCjB,MAAMI,aAAa,GAAG/C,MAAM,CAAC4C,IAA0B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI7B,KAAK,IAAI;EACT,QAAQA,KAAK,CAACiC,QAAQ;IACpB,KAAK,MAAM;MACT,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIF,aAAa;AAiCnB,MAAMG,aAAa,GAAGlD,MAAM,CAACW,GAAG;AAChC;AACA;AACA,CAAC;AAACwC,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGpD,MAAM,CAACW,GAAwB;AACnD,aAAaI,KAAK,IAAIA,KAAK,CAACsC,MAAM,GAAG,MAAM,GAAG,MAAM;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAXIF,WAAW;AAajB,MAAMG,YAAY,GAAGvD,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GARID,YAAY;AAUlB,MAAME,UAAU,GAAGzD,MAAM,CAAC0D,EAAE;AAC5B;AACA;AACA,CAAC;AAACC,IAAA,GAHIF,UAAU;AAKhB,MAAMG,QAAQ,GAAG5D,MAAM,CAAC6D,QAAQ;AAChC;AACA;AACA;AACA,sBAAsB9C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACyC,IAAA,GAbIF,QAAQ;AAed,MAAMG,YAAY,GAAG/D,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACqD,IAAA,GALID,YAAY;AAOlB,MAAME,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAiB,EAAE,CAAC;EAClE,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,gBAAgB,CAAC;EAClE,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAA8B,IAAI,CAAC;EACnF,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoF,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAMsF,QAAQ,GAAGpF,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACdsF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;EAElB,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,QAAQ,GAAG,MAAMhF,UAAU,CAACiF,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAEf,YAAY,KAAK,KAAK,GAAGgB,SAAS,GAAGhB,YAAY,CAAC;MACrGH,cAAc,CAACiB,QAAQ,CAACG,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACArB,cAAc,CAAC,CACb;QACEuB,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,eAAe;QAC7BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,WAAW;QACrBjD,MAAM,EAAE,gBAAgB;QACxBkD,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,sBAAsB;QACrCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,aAAa;QAC7BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEV,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,cAAc;QAC5BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,UAAU;QACpBjD,MAAM,EAAE,gBAAgB;QACxBkD,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,sBAAsB;QACrCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,aAAa,GAAGnC,WAAW,CAACoC,MAAM,CAACC,IAAI,IAC3CA,IAAI,CAACZ,YAAY,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC,IAClED,IAAI,CAACX,YAAY,CAACa,QAAQ,CAACjC,UAAU,CAAC,IACtC+B,IAAI,CAACV,QAAQ,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC,IAC7DD,IAAI,CAACL,cAAc,IAAIK,IAAI,CAACL,cAAc,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CAC7F,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,uBAAuB;EAAE,CAAC,EACpF;IAAEyB,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEE,MAAM,EAAE;EAAK,CAAC,EACpD;IAAEH,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,qBAAqB;EAAE,CAAC,EAChF;IAAEyB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,MAAM;IAAEC,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,kBAAkB;EAAE,CAAC,CAC3E;EAED,MAAM6B,kBAAkB,GAAGA,CAACC,IAAkB,EAAEC,MAA4B,KAAK;IAC/EtC,eAAe,CAACqC,IAAI,CAAC;IACrBnC,eAAe,CAACoC,MAAM,CAAC;IACvBlC,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACxC,YAAY,IAAI,CAACE,YAAY,EAAE;IAEpC,IAAI;MACF,MAAMuC,SAAS,GAAGvC,YAAY,KAAK,SAAS,GAAG,UAAU,GAAG,UAAU;MACtE,MAAMxE,UAAU,CAACgH,gBAAgB,CAC/B1C,YAAY,CAACgB,MAAM,EACnByB,SAAS,EACTrC,cAAc,EACdF,YAAY,KAAK,QAAQ,GAAGI,eAAe,GAAGM,SAChD,CAAC;;MAED;MACAnB,cAAc,CAACkD,KAAK,IAClBA,KAAK,CAACC,GAAG,CAACf,IAAI,IACZA,IAAI,CAACb,MAAM,KAAKhB,YAAY,CAACgB,MAAM,GAC/B;QAAE,GAAGa,IAAI;QAAE3D,MAAM,EAAEuE;MAAU,CAAC,GAC9BZ,IACN,CACF,CAAC;;MAED;MACA5B,eAAe,CAAC,IAAI,CAAC;MACrBE,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,IAAIP,YAAY,KAAK,KAAK,EAAE;QAC1Ba,eAAe,CAAC,CAAC;MACnB;IAEF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD+B,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,WAAW,GAAI5B,aAAqB,IAAK;IAC7C,MAAM6B,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,IAAIN,IAAI,CAAC1B,aAAa,CAAC,CAACiC,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACrG,IAAIJ,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAM;IAC/B,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,QAAQ;IACjC,OAAO,KAAK;EACd,CAAC;EAED,MAAMK,iBAAiB,GAAIlC,aAAqB,IAAK;IACnD,MAAM6B,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,IAAIN,IAAI,CAAC1B,aAAa,CAAC,CAACiC,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACrG,OAAO,GAAGJ,QAAQ,OAAOA,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;EACtD,CAAC;EAED,IAAIzD,OAAO,EAAE;IACX,oBACE9D,OAAA,CAACN,eAAe;MAACmI,KAAK,EAAC,cAAc;MAACzB,eAAe,EAAEA,eAAgB;MAAA0B,QAAA,eACrE9H,OAAA,CAACH,cAAc;QAAAkI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACElI,OAAA,CAACN,eAAe;IAACmI,KAAK,EAAC,cAAc;IAACzB,eAAe,EAAEA,eAAgB;IAAA0B,QAAA,gBACrE9H,OAAA,CAACL,IAAI;MAAAmI,QAAA,gBACH9H,OAAA;QAAImI,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAP,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3ElI,OAAA,CAACG,eAAe;QAAA2H,QAAA,gBACd9H,OAAA,CAACM,YAAY;UAACgI,KAAK,EAAEtE,YAAa;UAACuE,QAAQ,EAAGC,CAAC,IAAKvE,eAAe,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAR,QAAA,gBAClF9H,OAAA;YAAQsI,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtDlI,OAAA;YAAQsI,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClI,OAAA;YAAQsI,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClI,OAAA;YAAQsI,KAAK,EAAC,KAAK;YAAAR,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEflI,OAAA,CAACgB,WAAW;UACV0H,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,oDAAoD;UAChEL,KAAK,EAAEpE,UAAW;UAClBqE,QAAQ,EAAGC,CAAC,IAAKrE,aAAa,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAElBlI,OAAA,CAACmB,cAAc;QAAA2G,QAAA,eACb9H,OAAA,CAACqB,KAAK;UAAAyG,QAAA,gBACJ9H,OAAA;YAAA8H,QAAA,eACE9H,OAAA;cAAA8H,QAAA,gBACE9H,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpClI,OAAA,CAACwB,WAAW;gBAAAsG,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlI,OAAA;YAAA8H,QAAA,EACG/B,aAAa,CAACiB,GAAG,CAAEf,IAAI,iBACtBjG,OAAA,CAACiC,QAAQ;cAAA6F,QAAA,gBACP9H,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EAAE7B,IAAI,CAACZ;cAAY;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ClI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EAAE7B,IAAI,CAACX;cAAY;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ClI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EAAE7B,IAAI,CAACV;cAAQ;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtClI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EAAE7B,IAAI,CAACL,cAAc,IAAI;cAAG;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnDlI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,eACR9H,OAAA,CAACoC,WAAW;kBAACE,MAAM,EAAE2D,IAAI,CAAC3D,MAAO;kBAAAwF,QAAA,EAC9B7B,IAAI,CAAC3D,MAAM,CAACsG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZlI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EACP7B,IAAI,CAACP,aAAa,iBACjB1F,OAAA,CAACwC,aAAa;kBAACC,QAAQ,EAAE6E,WAAW,CAACrB,IAAI,CAACP,aAAa,CAAE;kBAAAoC,QAAA,EACtDR,WAAW,CAACrB,IAAI,CAACP,aAAa,CAAC,CAACmD,WAAW,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAChB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACZlI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EACP7B,IAAI,CAACP,aAAa,GAAGwB,UAAU,CAACjB,IAAI,CAACP,aAAa,CAAC,GAAG;cAAG;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACZlI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,EACP7B,IAAI,CAACP,aAAa,GAAGkC,iBAAiB,CAAC3B,IAAI,CAACP,aAAa,CAAC,GAAG;cAAG;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACZlI,OAAA,CAAC8B,SAAS;gBAAAgG,QAAA,eACR9H,OAAA,CAAC2C,aAAa;kBAAAmF,QAAA,gBACZ9H,OAAA,CAACJ,MAAM;oBACLkJ,IAAI,EAAC,IAAI;oBACTvC,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,SAASqB,IAAI,CAACb,MAAM,EAAE,CAAE;oBAAA0C,QAAA,EACjD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACRjC,IAAI,CAAC3D,MAAM,KAAK,gBAAgB,iBAC/BtC,OAAA,CAAAE,SAAA;oBAAA4H,QAAA,gBACE9H,OAAA,CAACJ,MAAM;sBACLkJ,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAC,WAAW;sBACnBxC,OAAO,EAAEA,CAAA,KAAME,kBAAkB,CAACR,IAAI,EAAE,SAAS,CAAE;sBAAA6B,QAAA,EACpD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTlI,OAAA,CAACJ,MAAM;sBACLkJ,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAC,QAAQ;sBAChBxC,OAAO,EAAEA,CAAA,KAAME,kBAAkB,CAACR,IAAI,EAAE,QAAQ,CAAE;sBAAA6B,QAAA,EACnD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDCjC,IAAI,CAACb,MAAM;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDhB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBnC,aAAa,CAACiD,MAAM,KAAK,CAAC,iBACzBhJ,OAAA;QAAKmI,KAAK,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEb,KAAK,EAAE;QAAO,CAAE;QAAAP,QAAA,EACjE5D,UAAU,GAAG,sCAAsC,GAAG;MAA0B;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPlI,OAAA,CAAC6C,WAAW;MAACC,MAAM,EAAE,CAAC,CAACsB,YAAY,IAAI,CAAC,CAACE,YAAa;MAAAwD,QAAA,eACpD9H,OAAA,CAACgD,YAAY;QAAA8E,QAAA,gBACX9H,OAAA,CAACkD,UAAU;UAAA4E,QAAA,GACRxD,YAAY,KAAK,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAC,UAAQ,EAACF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiB,YAAY;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEblI,OAAA;UAAKmI,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC9H,OAAA;YAAOmI,KAAK,EAAE;cAAEgB,OAAO,EAAE,OAAO;cAAEf,YAAY,EAAE,KAAK;cAAEgB,UAAU,EAAE;YAAM,CAAE;YAAAtB,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlI,OAAA,CAACqD,QAAQ;YACPiF,KAAK,EAAE9D,cAAe;YACtB+D,QAAQ,EAAGC,CAAC,IAAK/D,iBAAiB,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDK,WAAW,EAAC;UAA6B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL5D,YAAY,KAAK,QAAQ,iBACxBtE,OAAA;UAAKmI,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACnC9H,OAAA;YAAOmI,KAAK,EAAE;cAAEgB,OAAO,EAAE,OAAO;cAAEf,YAAY,EAAE,KAAK;cAAEgB,UAAU,EAAE;YAAM,CAAE;YAAAtB,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlI,OAAA,CAACM,YAAY;YACXgI,KAAK,EAAE5D,eAAgB;YACvB6D,QAAQ,EAAGC,CAAC,IAAK7D,kBAAkB,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACpDH,KAAK,EAAE;cAAEkB,KAAK,EAAE;YAAO,CAAE;YAAAvB,QAAA,gBAEzB9H,OAAA;cAAQsI,KAAK,EAAC,EAAE;cAAAR,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClI,OAAA;cAAQsI,KAAK,EAAC,sBAAsB;cAAAR,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClElI,OAAA;cAAQsI,KAAK,EAAC,cAAc;cAAAR,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzDlI,OAAA;cAAQsI,KAAK,EAAC,qBAAqB;cAAAR,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChElI,OAAA;cAAQsI,KAAK,EAAC,uBAAuB;cAAAR,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpElI,OAAA;cAAQsI,KAAK,EAAC,OAAO;cAAAR,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACN,eAEDlI,OAAA,CAACwD,YAAY;UAAAsE,QAAA,gBACX9H,OAAA,CAACJ,MAAM;YACLmJ,OAAO,EAAC,SAAS;YACjBxC,OAAO,EAAEA,CAAA,KAAM;cACblC,eAAe,CAAC,IAAI,CAAC;cACrBE,eAAe,CAAC,IAAI,CAAC;YACvB,CAAE;YAAAuD,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlI,OAAA,CAACJ,MAAM;YACLmJ,OAAO,EAAEzE,YAAY,KAAK,SAAS,GAAG,WAAW,GAAG,QAAS;YAC7DiC,OAAO,EAAEK,YAAa;YACtB0C,QAAQ,EAAE,CAAC9E,cAAc,IAAKF,YAAY,KAAK,QAAQ,IAAI,CAACI,eAAiB;YAAAoD,QAAA,EAE5ExD,YAAY,KAAK,SAAS,GAAG,SAAS,GAAG;UAAQ;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEtB,CAAC;AAACvE,EAAA,CA7SID,gBAA0B;EAAA,QASblE,WAAW;AAAA;AAAA+J,IAAA,GATxB7F,gBAA0B;AA+ShC,eAAeA,gBAAgB;AAAC,IAAArD,EAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA8F,IAAA;AAAAC,YAAA,CAAAnJ,EAAA;AAAAmJ,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}