/* Custom CSS for UBI-CPV API Swagger UI */

/* Header styling */
.swagger-ui .topbar {
    background-color: #1f4e79;
    border-bottom: 3px solid #ff6b35;
}

.swagger-ui .topbar .download-url-wrapper {
    display: none;
}

/* Info section styling */
.swagger-ui .info {
    margin: 20px 0;
}

.swagger-ui .info .title {
    color: #1f4e79;
    font-size: 2.5em;
    font-weight: bold;
}

.swagger-ui .info .description {
    color: #333;
    font-size: 1.1em;
    line-height: 1.6;
}

/* Operation styling */
.swagger-ui .opblock.opblock-post {
    border-color: #49cc90;
    background: rgba(73, 204, 144, 0.1);
}

.swagger-ui .opblock.opblock-get {
    border-color: #61affe;
    background: rgba(97, 175, 254, 0.1);
}

.swagger-ui .opblock.opblock-put {
    border-color: #fca130;
    background: rgba(252, 161, 48, 0.1);
}

.swagger-ui .opblock.opblock-delete {
    border-color: #f93e3e;
    background: rgba(249, 62, 62, 0.1);
}

/* Button styling */
.swagger-ui .btn.authorize {
    background-color: #1f4e79;
    border-color: #1f4e79;
}

.swagger-ui .btn.authorize:hover {
    background-color: #2a5a8a;
    border-color: #2a5a8a;
}

/* Schema styling */
.swagger-ui .model-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

/* Response styling */
.swagger-ui .responses-inner h4 {
    color: #1f4e79;
    font-weight: bold;
}

/* Custom branding */
.swagger-ui .topbar .link:after {
    content: "UBI-CPV API";
    color: white;
    font-weight: bold;
    font-size: 1.2em;
    margin-left: 10px;
}

/* Hide Swagger logo */
.swagger-ui .topbar .link img {
    display: none;
}

/* Improve readability */
.swagger-ui .parameter__name {
    font-weight: bold;
    color: #1f4e79;
}

.swagger-ui .parameter__type {
    color: #666;
    font-style: italic;
}

/* Status code styling */
.swagger-ui .response-col_status {
    font-weight: bold;
}

.swagger-ui .response-col_status.response-200 {
    color: #49cc90;
}

.swagger-ui .response-col_status.response-400,
.swagger-ui .response-col_status.response-401,
.swagger-ui .response-col_status.response-403,
.swagger-ui .response-col_status.response-404,
.swagger-ui .response-col_status.response-500 {
    color: #f93e3e;
}
