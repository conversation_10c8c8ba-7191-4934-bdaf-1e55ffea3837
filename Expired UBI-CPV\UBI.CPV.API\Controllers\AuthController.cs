using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Swashbuckle.AspNetCore.Annotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using UBI.CPV.API.Data;
using UBI.CPV.API.Models.DTOs;
using UBI.CPV.API.Models.Entities;
using BCrypt.Net;

namespace UBI.CPV.API.Controllers
{
    /// <summary>
    /// Authentication controller for user login, logout, and token management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [SwaggerTag("Authentication endpoints for user login, logout, and token management")]
    public class AuthController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(ApplicationDbContext context, IConfiguration configuration, ILogger<AuthController> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Authenticates a user and returns a JWT token
        /// </summary>
        /// <param name="request">Login credentials including username, password, and role</param>
        /// <returns>Login response with JWT token and user information</returns>
        /// <response code="200">Login successful or failed with appropriate message</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("login")]
        [SwaggerOperation(
            Summary = "User Login",
            Description = "Authenticates a user with username, password, and role. Returns JWT token on successful authentication.",
            OperationId = "Login"
        )]
        [SwaggerResponse(200, "Login response", typeof(LoginResponseDto))]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto request)
        {
            try
            {
                // Find user by username and role
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username &&
                                            u.Role == request.Role &&
                                            u.IsActive);

                if (user == null)
                {
                    return Ok(new LoginResponseDto
                    {
                        Success = false,
                        Message = "Invalid username, password, or role."
                    });
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                {
                    return Ok(new LoginResponseDto
                    {
                        Success = false,
                        Message = "Invalid username, password, or role."
                    });
                }

                // Generate JWT token
                var token = GenerateJwtToken(user);
                var refreshToken = GenerateRefreshToken();
                var expiresAt = DateTime.UtcNow.AddHours(8); // 8 hours

                // Save session
                var session = new UserSession
                {
                    UserId = user.UserId,
                    Token = token,
                    RefreshToken = refreshToken,
                    ExpiresAt = expiresAt,
                    IsActive = true
                };

                _context.UserSessions.Add(session);

                // Update last login date
                user.LastLoginDate = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                var userDto = new UserDto
                {
                    UserId = user.UserId,
                    Username = user.Username,
                    Email = user.Email,
                    Role = user.Role,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    IsActive = user.IsActive,
                    CreatedDate = user.CreatedDate,
                    LastLoginDate = user.LastLoginDate
                };

                return Ok(new LoginResponseDto
                {
                    Success = true,
                    Message = "Login successful",
                    Token = token,
                    RefreshToken = refreshToken,
                    User = userDto,
                    ExpiresAt = expiresAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", request.Username);
                return StatusCode(500, new LoginResponseDto
                {
                    Success = false,
                    Message = "An error occurred during login."
                });
            }
        }

        /// <summary>
        /// Logs out the current user by invalidating their session
        /// </summary>
        /// <returns>Logout confirmation</returns>
        /// <response code="200">Logout successful</response>
        /// <response code="401">Unauthorized - invalid or missing token</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("logout")]
        [Authorize]
        [SwaggerOperation(
            Summary = "User Logout",
            Description = "Invalidates the current user session and logs out the user.",
            OperationId = "Logout"
        )]
        [SwaggerResponse(200, "Logout successful")]
        [SwaggerResponse(401, "Unauthorized")]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var token = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var session = await _context.UserSessions
                    .FirstOrDefaultAsync(s => s.Token == token && s.IsActive);

                if (session != null)
                {
                    session.IsActive = false;
                    await _context.SaveChangesAsync();
                }

                return Ok(new { Success = true, Message = "Logout successful" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, new { Success = false, Message = "An error occurred during logout." });
            }
        }

        /// <summary>
        /// Refreshes an expired JWT token using a refresh token
        /// </summary>
        /// <param name="request">Refresh token request containing the current token and refresh token</param>
        /// <returns>New JWT token and refresh token</returns>
        /// <response code="200">Token refresh successful or failed with appropriate message</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("refresh-token")]
        [SwaggerOperation(
            Summary = "Refresh JWT Token",
            Description = "Generates a new JWT token using a valid refresh token.",
            OperationId = "RefreshToken"
        )]
        [SwaggerResponse(200, "Token refresh response", typeof(LoginResponseDto))]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<ActionResult<LoginResponseDto>> RefreshToken([FromBody] RefreshTokenDto request)
        {
            try
            {
                var session = await _context.UserSessions
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.Token == request.Token &&
                                            s.RefreshToken == request.RefreshToken &&
                                            s.IsActive &&
                                            s.ExpiresAt > DateTime.UtcNow);

                if (session == null)
                {
                    return Ok(new LoginResponseDto
                    {
                        Success = false,
                        Message = "Invalid or expired refresh token."
                    });
                }

                // Generate new tokens
                var newToken = GenerateJwtToken(session.User);
                var newRefreshToken = GenerateRefreshToken();
                var newExpiresAt = DateTime.UtcNow.AddHours(8);

                // Update session
                session.Token = newToken;
                session.RefreshToken = newRefreshToken;
                session.ExpiresAt = newExpiresAt;

                await _context.SaveChangesAsync();

                var userDto = new UserDto
                {
                    UserId = session.User.UserId,
                    Username = session.User.Username,
                    Email = session.User.Email,
                    Role = session.User.Role,
                    FirstName = session.User.FirstName,
                    LastName = session.User.LastName,
                    PhoneNumber = session.User.PhoneNumber,
                    IsActive = session.User.IsActive,
                    CreatedDate = session.User.CreatedDate,
                    LastLoginDate = session.User.LastLoginDate
                };

                return Ok(new LoginResponseDto
                {
                    Success = true,
                    Message = "Token refreshed successfully",
                    Token = newToken,
                    RefreshToken = newRefreshToken,
                    User = userDto,
                    ExpiresAt = newExpiresAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, new LoginResponseDto
                {
                    Success = false,
                    Message = "An error occurred during token refresh."
                });
            }
        }

        /// <summary>
        /// Gets the current authenticated user's information
        /// </summary>
        /// <returns>Current user information</returns>
        /// <response code="200">User information retrieved successfully</response>
        /// <response code="401">Unauthorized - invalid or missing token</response>
        /// <response code="404">User not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("me")]
        [Authorize]
        [SwaggerOperation(
            Summary = "Get Current User",
            Description = "Retrieves the information of the currently authenticated user.",
            OperationId = "GetCurrentUser"
        )]
        [SwaggerResponse(200, "User information", typeof(UserDto))]
        [SwaggerResponse(401, "Unauthorized")]
        [SwaggerResponse(404, "User not found")]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<ActionResult<UserDto>> GetCurrentUser()
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.UserId == userId && u.IsActive);

                if (user == null)
                {
                    return NotFound(new { Message = "User not found" });
                }

                var userDto = new UserDto
                {
                    UserId = user.UserId,
                    Username = user.Username,
                    Email = user.Email,
                    Role = user.Role,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    IsActive = user.IsActive,
                    CreatedDate = user.CreatedDate,
                    LastLoginDate = user.LastLoginDate
                };

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user");
                return StatusCode(500, new { Message = "An error occurred while getting user information." });
            }
        }

        private string GenerateJwtToken(User user)
        {
            var jwtKey = _configuration["Jwt:Key"] ?? "your-super-secret-jwt-key-that-is-at-least-32-characters-long";
            var key = Encoding.ASCII.GetBytes(jwtKey);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Role, user.Role),
                new Claim("FirstName", user.FirstName),
                new Claim("LastName", user.LastName)
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(8),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = _configuration["Jwt:Issuer"] ?? "UBI-CPV-API",
                Audience = _configuration["Jwt:Audience"] ?? "UBI-CPV-Client"
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateRefreshToken()
        {
            return Guid.NewGuid().ToString();
        }
    }
}
