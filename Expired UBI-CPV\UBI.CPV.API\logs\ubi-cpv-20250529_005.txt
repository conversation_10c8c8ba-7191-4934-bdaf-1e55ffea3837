2025-05-29 18:45:00.305 +05:30 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:45:00.478 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:45:00.510 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:45:00.534 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:45:00.578 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 18:45:00.647 +05:30 [INF] Applying migration '20250529130711_InitialCreate'.
2025-05-29 18:45:00.841 +05:30 [ERR] Failed executing DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
);
2025-05-29 18:45:01.010 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:80448c5e-53d1-4b2e-90e7-f1a13782a954
Error Number:2714,State:6,Class:16
2025-05-29 18:45:01.117 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 18:45:01.223 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 18:45:01.800 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 18:45:01.822 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 18:45:01.922 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 18:45:01.928 +05:30 [INF] Hosting environment: Development
2025-05-29 18:45:01.933 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-29 18:45:03.070 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null
2025-05-29 18:45:03.739 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 681.9606ms
2025-05-29 18:45:03.818 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404
