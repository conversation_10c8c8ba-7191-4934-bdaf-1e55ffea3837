import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem } from '../../services/apiService';

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'assigned':
        return `
          background-color: #fff3e0;
          color: #e65100;
        `;
      case 'in-progress':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const PriorityBadge = styled.span<{ priority: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.priority) {
      case 'high':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      case 'medium':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'low':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const AgentTasks: React.FC = () => {
  const [tasks, setTasks] = useState<LeadListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadTasks();
  }, [statusFilter]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const status = statusFilter === 'all' ? undefined : statusFilter;
      const response = await apiService.getLeads(1, 100, status);
      setTasks(response.data || []);
    } catch (error) {
      console.error('Error loading tasks:', error);
      // Mock data for demo
      setTasks([
        {
          leadId: 1,
          customerName: 'John Doe',
          mobileNumber: '9876543210',
          loanType: 'Personal Loan',
          status: 'assigned',
          createdDate: '2024-01-15T10:30:00Z',
          assignedDate: '2024-01-15T11:00:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 0,
          croppedImageCount: 0,
        },
        {
          leadId: 2,
          customerName: 'Jane Smith',
          mobileNumber: '9876543211',
          loanType: 'Home Loan',
          status: 'in-progress',
          createdDate: '2024-01-14T09:15:00Z',
          assignedDate: '2024-01-14T10:00:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 2,
          croppedImageCount: 1,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filteredTasks = tasks.filter(task =>
    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.mobileNumber.includes(searchTerm) ||
    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },
    { icon: '📋', label: 'My Tasks', active: true },
    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },
  ];

  const handleTaskAction = async (leadId: number, action: string) => {
    try {
      if (action === 'start') {
        await apiService.updateLeadStatus(leadId, 'in-progress', 'Started verification process');
      } else if (action === 'submit') {
        await apiService.updateLeadStatus(leadId, 'pending-review', 'Submitted for review');
      }
      loadTasks(); // Reload tasks after action
    } catch (error) {
      console.error('Error updating task:', error);
      alert('Failed to update task');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getPriority = (createdDate: string) => {
    const daysDiff = Math.floor((Date.now() - new Date(createdDate).getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff > 3) return 'high';
    if (daysDiff > 1) return 'medium';
    return 'low';
  };

  if (loading) {
    return (
      <DashboardLayout title="My Tasks" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="My Tasks" navigationItems={navigationItems}>
      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Assigned Tasks</h2>

        <FilterContainer>
          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
            <option value="all">All Tasks</option>
            <option value="assigned">Assigned</option>
            <option value="in-progress">In Progress</option>
          </FilterSelect>
          
          <SearchInput
            type="text"
            placeholder="Search by customer name, mobile, or loan type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </FilterContainer>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Priority</TableHeader>
                <TableHeader>Assigned Date</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {filteredTasks.map((task) => (
                <TableRow key={task.leadId}>
                  <TableCell>{task.customerName}</TableCell>
                  <TableCell>{task.mobileNumber}</TableCell>
                  <TableCell>{task.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={task.status}>
                      {task.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    <PriorityBadge priority={getPriority(task.createdDate)}>
                      {getPriority(task.createdDate).toUpperCase()}
                    </PriorityBadge>
                  </TableCell>
                  <TableCell>
                    {task.assignedDate ? formatDate(task.assignedDate) : '-'}
                  </TableCell>
                  <TableCell>
                    <ActionButtons>
                      <Button
                        size="sm"
                        onClick={() => navigate(`/lead/${task.leadId}`)}
                      >
                        View
                      </Button>
                      {task.status === 'assigned' && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleTaskAction(task.leadId, 'start')}
                        >
                          Start
                        </Button>
                      )}
                      {task.status === 'in-progress' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTaskAction(task.leadId, 'submit')}
                        >
                          Submit
                        </Button>
                      )}
                    </ActionButtons>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {filteredTasks.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            {searchTerm ? 'No tasks found matching your search.' : 'No tasks assigned yet.'}
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
};

export default AgentTasks;
