import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService } from '../../services/apiService';

const ReportsContainer = styled.div`
  display: grid;
  gap: 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #007E3A, #005a2a);
  color: white;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
`;

const ChartContainer = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled(Card)`
  padding: 20px;
`;

const ChartTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
  text-align: center;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const MetricsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const TrendIndicator = styled.span<{ trend: 'up' | 'down' | 'stable' }>`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  
  ${props => {
    switch (props.trend) {
      case 'up':
        return `color: #2e7d32;`;
      case 'down':
        return `color: #c62828;`;
      case 'stable':
        return `color: #666;`;
    }
  }}
`;

const AdminReports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState('month');
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    totalLeads: 0,
    completedLeads: 0,
    pendingLeads: 0,
    rejectedLeads: 0,
    averageProcessingTime: 0,
    systemEfficiency: 0,
    activeAgents: 0,
  });
  const [departmentMetrics, setDepartmentMetrics] = useState<any[]>([]);
  const [monthlyTrends, setMonthlyTrends] = useState<any[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    loadReportsData();
  }, [timeFilter]);

  const loadReportsData = async () => {
    try {
      setLoading(true);
      
      const [dashboardStats, users] = await Promise.all([
        apiService.getDashboardStats(),
        apiService.getUsers(),
      ]);

      setSystemStats({
        totalUsers: users.length || 0,
        totalLeads: dashboardStats.totalLeads || 0,
        completedLeads: dashboardStats.completedLeads || 0,
        pendingLeads: dashboardStats.pendingLeads || 0,
        rejectedLeads: dashboardStats.rejectedLeads || 0,
        averageProcessingTime: 2.8, // Mock data
        systemEfficiency: 87, // Mock data
        activeAgents: users.filter(u => u.role === 'Agent' && u.isActive).length || 0,
      });

      // Mock department metrics
      setDepartmentMetrics([
        {
          department: 'Personal Loans',
          totalLeads: 150,
          completed: 128,
          pending: 15,
          rejected: 7,
          avgTime: 2.5,
          efficiency: 89,
        },
        {
          department: 'Home Loans',
          totalLeads: 89,
          completed: 76,
          pending: 8,
          rejected: 5,
          avgTime: 3.2,
          efficiency: 85,
        },
        {
          department: 'Car Loans',
          totalLeads: 67,
          completed: 58,
          pending: 6,
          rejected: 3,
          avgTime: 2.1,
          efficiency: 92,
        },
      ]);

      // Mock monthly trends
      setMonthlyTrends([
        { month: 'Jan', leads: 120, completed: 105, efficiency: 87 },
        { month: 'Feb', leads: 135, completed: 118, efficiency: 89 },
        { month: 'Mar', leads: 156, completed: 142, efficiency: 91 },
        { month: 'Apr', leads: 142, completed: 128, efficiency: 88 },
      ]);

    } catch (error) {
      console.error('Error loading reports data:', error);
      // Use mock data on error
      setSystemStats({
        totalUsers: 25,
        totalLeads: 306,
        completedLeads: 262,
        pendingLeads: 29,
        rejectedLeads: 15,
        averageProcessingTime: 2.8,
        systemEfficiency: 87,
        activeAgents: 15,
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/admin/dashboard') },
    { icon: '👥', label: 'Users', onClick: () => navigate('/admin/users') },
    { icon: '📋', label: 'Leads', onClick: () => navigate('/admin/leads') },
    { icon: '📊', label: 'Reports', active: true },
    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },
  ];

  const getCompletionRate = () => {
    return systemStats.totalLeads > 0 ? Math.round((systemStats.completedLeads / systemStats.totalLeads) * 100) : 0;
  };

  const getSuccessRate = () => {
    const totalProcessed = systemStats.completedLeads + systemStats.rejectedLeads;
    return totalProcessed > 0 ? Math.round((systemStats.completedLeads / totalProcessed) * 100) : 0;
  };

  if (loading) {
    return (
      <DashboardLayout title="System Reports" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="System Reports" navigationItems={navigationItems}>
      <ReportsContainer>
        <FilterContainer>
          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </FilterSelect>
          
          <Button variant="outline" onClick={() => window.print()}>
            📄 Export Report
          </Button>
          
          <Button variant="secondary" onClick={() => navigate('/admin/analytics')}>
            📈 Advanced Analytics
          </Button>
        </FilterContainer>

        {/* System Overview Stats */}
        <StatsGrid>
          <StatCard>
            <StatValue>{systemStats.totalUsers}</StatValue>
            <StatLabel>Total Users</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.activeAgents}</StatValue>
            <StatLabel>Active Agents</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.totalLeads}</StatValue>
            <StatLabel>Total Leads</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.completedLeads}</StatValue>
            <StatLabel>Completed</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.pendingLeads}</StatValue>
            <StatLabel>Pending</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.rejectedLeads}</StatValue>
            <StatLabel>Rejected</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.averageProcessingTime}</StatValue>
            <StatLabel>Avg. Days</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{systemStats.systemEfficiency}%</StatValue>
            <StatLabel>System Efficiency</StatLabel>
          </StatCard>
        </StatsGrid>

        {/* Charts and Trends */}
        <ChartContainer>
          <ChartCard>
            <ChartTitle>Monthly Performance Trends</ChartTitle>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '15px', textAlign: 'center' }}>
              {monthlyTrends.map((trend, index) => (
                <div key={index}>
                  <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px' }}>
                    {trend.month}
                  </div>
                  <div style={{ fontSize: '18px', color: '#007E3A', fontWeight: 'bold' }}>
                    {trend.completed}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    of {trend.leads}
                  </div>
                  <div style={{ fontSize: '12px', color: '#2e7d32', marginTop: '4px' }}>
                    {trend.efficiency}% eff.
                  </div>
                </div>
              ))}
            </div>
          </ChartCard>

          <ChartCard>
            <ChartTitle>System Health</ChartTitle>
            <div style={{ display: 'grid', gap: '15px' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '32px', color: '#2e7d32', fontWeight: 'bold' }}>
                  {getCompletionRate()}%
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>Completion Rate</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '32px', color: '#007E3A', fontWeight: 'bold' }}>
                  {getSuccessRate()}%
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>Success Rate</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '32px', color: '#FFD100', fontWeight: 'bold' }}>
                  {systemStats.systemEfficiency}%
                </div>
                <div style={{ fontSize: '14px', color: '#666' }}>System Efficiency</div>
              </div>
            </div>
          </ChartCard>
        </ChartContainer>

        {/* Department Performance */}
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Department Performance</h3>
          <div style={{ overflowX: 'auto' }}>
            <MetricsTable>
              <thead>
                <tr>
                  <TableHeader>Department</TableHeader>
                  <TableHeader>Total Leads</TableHeader>
                  <TableHeader>Completed</TableHeader>
                  <TableHeader>Pending</TableHeader>
                  <TableHeader>Rejected</TableHeader>
                  <TableHeader>Avg. Time</TableHeader>
                  <TableHeader>Efficiency</TableHeader>
                  <TableHeader>Trend</TableHeader>
                </tr>
              </thead>
              <tbody>
                {departmentMetrics.map((dept, index) => (
                  <TableRow key={index}>
                    <TableCell style={{ fontWeight: '500' }}>{dept.department}</TableCell>
                    <TableCell>{dept.totalLeads}</TableCell>
                    <TableCell style={{ color: '#2e7d32' }}>{dept.completed}</TableCell>
                    <TableCell style={{ color: '#ff8f00' }}>{dept.pending}</TableCell>
                    <TableCell style={{ color: '#c62828' }}>{dept.rejected}</TableCell>
                    <TableCell>{dept.avgTime} days</TableCell>
                    <TableCell>{dept.efficiency}%</TableCell>
                    <TableCell>
                      <TrendIndicator trend={dept.efficiency > 88 ? 'up' : dept.efficiency > 85 ? 'stable' : 'down'}>
                        {dept.efficiency > 88 ? '↗️' : dept.efficiency > 85 ? '➡️' : '↘️'}
                        {dept.efficiency > 88 ? 'Improving' : dept.efficiency > 85 ? 'Stable' : 'Declining'}
                      </TrendIndicator>
                    </TableCell>
                  </TableRow>
                ))}
              </tbody>
            </MetricsTable>
          </div>
        </Card>

        {/* Key Insights */}
        <Card>
          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Executive Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            <div>
              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Performance Highlights</h4>
              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
                <li>Car Loans department leads with 92% efficiency</li>
                <li>Overall system efficiency at 87%, up 3% from last month</li>
                <li>Average processing time reduced to 2.8 days</li>
              </ul>
            </div>
            <div>
              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Areas for Improvement</h4>
              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
                <li>Home Loans processing time needs optimization</li>
                <li>Rejection rate in Personal Loans requires attention</li>
                <li>Agent workload distribution could be improved</li>
              </ul>
            </div>
            <div>
              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Recommendations</h4>
              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
                <li>Implement best practices from Car Loans team</li>
                <li>Provide additional training for Home Loans agents</li>
                <li>Review and update verification guidelines</li>
              </ul>
            </div>
          </div>
        </Card>
      </ReportsContainer>
    </DashboardLayout>
  );
};

export default AdminReports;
