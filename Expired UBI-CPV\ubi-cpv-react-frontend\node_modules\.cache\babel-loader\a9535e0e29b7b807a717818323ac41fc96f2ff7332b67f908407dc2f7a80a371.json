{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Profile\\\\UserProfile.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c = ProfileContainer;\nconst ProfileCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 30px;\n`;\n_c2 = ProfileCard;\nconst Avatar = styled.div`\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36px;\n  color: white;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n_c3 = Avatar;\nconst UserName = styled.h2`\n  margin-bottom: 8px;\n  color: ${props => props.theme.colors.textDark};\n`;\n_c4 = UserName;\nconst UserRole = styled.div`\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 16px;\n  margin-bottom: 20px;\n`;\n_c5 = UserRole;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  background-color: ${props => props.isActive ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.isActive ? '#2e7d32' : '#c62828'};\n`;\n_c6 = StatusBadge;\nconst FormContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c7 = FormContainer;\nconst FormSection = styled(Card)`\n  padding: 20px;\n`;\n_c8 = FormSection;\nconst SectionTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  padding-bottom: 10px;\n`;\n_c9 = SectionTitle;\nconst FormGroup = styled.div`\n  margin-bottom: 20px;\n`;\n_c0 = FormGroup;\nconst Label = styled.label`\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c1 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n  \n  &:disabled {\n    background-color: ${props => props.theme.colors.lightGray};\n    cursor: not-allowed;\n  }\n`;\n_c10 = Input;\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n`;\n_c11 = TextArea;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n_c12 = ButtonGroup;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 15px;\n  margin-top: 20px;\n`;\n_c13 = StatsGrid;\nconst StatItem = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: ${props => props.theme.colors.offWhite};\n  border-radius: ${props => props.theme.borderRadius.sm};\n`;\n_c14 = StatItem;\nconst StatValue = styled.div`\n  font-size: 20px;\n  font-weight: bold;\n  color: #007E3A;\n  margin-bottom: 5px;\n`;\n_c15 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n_c16 = StatLabel;\nconst UserProfile = () => {\n  _s();\n  const {\n    user: currentUser\n  } = useAuth();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phoneNumber: '',\n    bio: ''\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [userStats, setUserStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvalRate: 0,\n    avgTime: 0\n  });\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadUserProfile();\n    loadUserStats();\n  }, []);\n  const loadUserProfile = async () => {\n    try {\n      setLoading(true);\n      const userData = await apiService.getCurrentUser();\n      setUser(userData);\n      setFormData({\n        firstName: userData.firstName || '',\n        lastName: userData.lastName || '',\n        email: userData.email || '',\n        phoneNumber: userData.phoneNumber || '',\n        bio: '' // This would come from user profile if available\n      });\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n      // Use current user from context as fallback\n      if (currentUser) {\n        setUser(currentUser);\n        setFormData({\n          firstName: currentUser.firstName || '',\n          lastName: currentUser.lastName || '',\n          email: currentUser.email || '',\n          phoneNumber: currentUser.phoneNumber || '',\n          bio: ''\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadUserStats = async () => {\n    try {\n      if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === 'Agent') {\n        const stats = await apiService.getAgentDashboardStats();\n        setUserStats({\n          totalTasks: stats.totalAssigned || 0,\n          completedTasks: stats.completedLeads || 0,\n          approvalRate: 85,\n          // Mock data\n          avgTime: 2.5 // Mock data\n        });\n      }\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => {\n      switch (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) {\n        case 'Agent':\n          navigate('/agent/dashboard');\n          break;\n        case 'Supervisor':\n          navigate('/supervisor/dashboard');\n          break;\n        case 'Admin':\n          navigate('/admin/dashboard');\n          break;\n        default:\n          navigate('/');\n      }\n    }\n  }, {\n    icon: '👤',\n    label: 'Profile',\n    active: true\n  }, {\n    icon: '⚙️',\n    label: 'Settings',\n    onClick: () => navigate('/settings')\n  }];\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handlePasswordChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setPasswordData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSaveProfile = async () => {\n    try {\n      if (!user) return;\n      await apiService.updateUser(user.userId, formData);\n\n      // Update local user state\n      setUser(prev => prev ? {\n        ...prev,\n        ...formData\n      } : null);\n      setEditing(false);\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile');\n    }\n  };\n  const handleChangePassword = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('New passwords do not match');\n      return;\n    }\n    if (passwordData.newPassword.length < 6) {\n      alert('Password must be at least 6 characters long');\n      return;\n    }\n    try {\n      // This would be a dedicated change password endpoint\n      alert('Password change functionality would be implemented here');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n    } catch (error) {\n      console.error('Error changing password:', error);\n      alert('Failed to change password');\n    }\n  };\n  const getInitials = (firstName, lastName) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"User Profile\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"User Profile\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px'\n          },\n          children: \"User profile not found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"User Profile\",\n    navigationItems: navigationItems,\n    children: /*#__PURE__*/_jsxDEV(ProfileContainer, {\n      children: [/*#__PURE__*/_jsxDEV(ProfileCard, {\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          children: getInitials(user.firstName, user.lastName)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserName, {\n          children: [user.firstName, \" \", user.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserRole, {\n          children: user.role\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatusBadge, {\n          isActive: user.isActive,\n          children: user.isActive ? 'Active' : 'Inactive'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Username:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), \" \", user.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), \" \", user.email]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Joined:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), \" \", formatDate(user.createdDate)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), user.lastLoginDate && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Last Login:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), \" \", formatDate(user.lastLoginDate)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), user.role === 'Agent' && /*#__PURE__*/_jsxDEV(StatsGrid, {\n          children: [/*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: userStats.totalTasks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Total Tasks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: userStats.completedTasks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: [userStats.approvalRate, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Approval Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatValue, {\n              children: userStats.avgTime\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"Avg. Days\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FormSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n              style: {\n                marginBottom: 0\n              },\n              children: \"Personal Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              size: \"sm\",\n              onClick: () => setEditing(!editing),\n              children: editing ? 'Cancel' : 'Edit'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"First Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                name: \"firstName\",\n                value: formData.firstName,\n                onChange: handleInputChange,\n                disabled: !editing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                name: \"lastName\",\n                value: formData.lastName,\n                onChange: handleInputChange,\n                disabled: !editing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              name: \"email\",\n              value: formData.email,\n              onChange: handleInputChange,\n              disabled: !editing\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              name: \"phoneNumber\",\n              value: formData.phoneNumber,\n              onChange: handleInputChange,\n              disabled: !editing\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Bio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n              name: \"bio\",\n              value: formData.bio,\n              onChange: handleInputChange,\n              disabled: !editing,\n              placeholder: \"Tell us about yourself...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), editing && /*#__PURE__*/_jsxDEV(ButtonGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              onClick: () => setEditing(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleSaveProfile,\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: \"Change Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Current Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"password\",\n              name: \"currentPassword\",\n              value: passwordData.currentPassword,\n              onChange: handlePasswordChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"password\",\n              name: \"newPassword\",\n              value: passwordData.newPassword,\n              onChange: handlePasswordChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"Confirm New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"password\",\n              name: \"confirmPassword\",\n              value: passwordData.confirmPassword,\n              onChange: handlePasswordChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              onClick: handleChangePassword,\n              disabled: !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword,\n              children: \"Change Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 344,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"L371QQrupiK4Z4PLqkHYMVcXYnw=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c17 = UserProfile;\nexport default UserProfile;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"ProfileContainer\");\n$RefreshReg$(_c2, \"ProfileCard\");\n$RefreshReg$(_c3, \"Avatar\");\n$RefreshReg$(_c4, \"UserName\");\n$RefreshReg$(_c5, \"UserRole\");\n$RefreshReg$(_c6, \"StatusBadge\");\n$RefreshReg$(_c7, \"FormContainer\");\n$RefreshReg$(_c8, \"FormSection\");\n$RefreshReg$(_c9, \"SectionTitle\");\n$RefreshReg$(_c0, \"FormGroup\");\n$RefreshReg$(_c1, \"Label\");\n$RefreshReg$(_c10, \"Input\");\n$RefreshReg$(_c11, \"TextArea\");\n$RefreshReg$(_c12, \"ButtonGroup\");\n$RefreshReg$(_c13, \"StatsGrid\");\n$RefreshReg$(_c14, \"StatItem\");\n$RefreshReg$(_c15, \"StatValue\");\n$RefreshReg$(_c16, \"StatLabel\");\n$RefreshReg$(_c17, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "useAuth", "jsxDEV", "_jsxDEV", "ProfileContainer", "div", "_c", "ProfileCard", "_c2", "Avatar", "_c3", "UserName", "h2", "props", "theme", "colors", "textDark", "_c4", "UserRole", "textMedium", "_c5", "StatusBadge", "span", "isActive", "_c6", "FormContainer", "_c7", "FormSection", "_c8", "SectionTitle", "h3", "lightGray", "_c9", "FormGroup", "_c0", "Label", "label", "_c1", "Input", "input", "mediumGray", "borderRadius", "sm", "primary", "_c10", "TextArea", "textarea", "_c11", "ButtonGroup", "_c12", "StatsGrid", "_c13", "StatItem", "offWhite", "_c14", "StatValue", "_c15", "StatLabel", "textLight", "_c16", "UserProfile", "_s", "user", "currentUser", "setUser", "loading", "setLoading", "editing", "setEditing", "formData", "setFormData", "firstName", "lastName", "email", "phoneNumber", "bio", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "userStats", "setUserStats", "totalTasks", "completedTasks", "approvalRate", "avgTime", "navigate", "loadUserProfile", "loadUserStats", "userData", "getCurrentUser", "error", "console", "role", "stats", "getAgentDashboardStats", "totalAssigned", "completedLeads", "navigationItems", "icon", "onClick", "active", "handleInputChange", "e", "name", "value", "target", "prev", "handlePasswordChange", "handleSaveProfile", "updateUser", "userId", "alert", "handleChangePassword", "length", "getInitials", "char<PERSON>t", "toUpperCase", "formatDate", "dateString", "Date", "toLocaleDateString", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "marginTop", "width", "marginBottom", "username", "createdDate", "lastLoginDate", "display", "justifyContent", "alignItems", "variant", "size", "gridTemplateColumns", "gap", "type", "onChange", "disabled", "placeholder", "_c17", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Profile/UserProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, User } from '../../services/apiService';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst ProfileContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ProfileCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 30px;\n`;\n\nconst Avatar = styled.div`\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36px;\n  color: white;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n\nconst UserName = styled.h2`\n  margin-bottom: 8px;\n  color: ${props => props.theme.colors.textDark};\n`;\n\nconst UserRole = styled.div`\n  color: ${props => props.theme.colors.textMedium};\n  font-size: 16px;\n  margin-bottom: 20px;\n`;\n\nconst StatusBadge = styled.span<{ isActive: boolean }>`\n  display: inline-block;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n  background-color: ${props => props.isActive ? '#e8f5e9' : '#ffebee'};\n  color: ${props => props.isActive ? '#2e7d32' : '#c62828'};\n`;\n\nconst FormContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst FormSection = styled(Card)`\n  padding: 20px;\n`;\n\nconst SectionTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  padding-bottom: 10px;\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n  \n  &:disabled {\n    background-color: ${props => props.theme.colors.lightGray};\n    cursor: not-allowed;\n  }\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: 10px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  resize: vertical;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);\n  }\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 15px;\n  margin-top: 20px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: ${props => props.theme.colors.offWhite};\n  border-radius: ${props => props.theme.borderRadius.sm};\n`;\n\nconst StatValue = styled.div`\n  font-size: 20px;\n  font-weight: bold;\n  color: #007E3A;\n  margin-bottom: 5px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  color: ${props => props.theme.colors.textLight};\n`;\n\nconst UserProfile: React.FC = () => {\n  const { user: currentUser } = useAuth();\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [editing, setEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phoneNumber: '',\n    bio: '',\n  });\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  const [userStats, setUserStats] = useState({\n    totalTasks: 0,\n    completedTasks: 0,\n    approvalRate: 0,\n    avgTime: 0,\n  });\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadUserProfile();\n    loadUserStats();\n  }, []);\n\n  const loadUserProfile = async () => {\n    try {\n      setLoading(true);\n      const userData = await apiService.getCurrentUser();\n      setUser(userData);\n      setFormData({\n        firstName: userData.firstName || '',\n        lastName: userData.lastName || '',\n        email: userData.email || '',\n        phoneNumber: userData.phoneNumber || '',\n        bio: '', // This would come from user profile if available\n      });\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n      // Use current user from context as fallback\n      if (currentUser) {\n        setUser(currentUser);\n        setFormData({\n          firstName: currentUser.firstName || '',\n          lastName: currentUser.lastName || '',\n          email: currentUser.email || '',\n          phoneNumber: currentUser.phoneNumber || '',\n          bio: '',\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadUserStats = async () => {\n    try {\n      if (currentUser?.role === 'Agent') {\n        const stats = await apiService.getAgentDashboardStats();\n        setUserStats({\n          totalTasks: stats.totalAssigned || 0,\n          completedTasks: stats.completedLeads || 0,\n          approvalRate: 85, // Mock data\n          avgTime: 2.5, // Mock data\n        });\n      }\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  };\n\n  const navigationItems = [\n    { \n      icon: '🏠', \n      label: 'Dashboard', \n      onClick: () => {\n        switch (currentUser?.role) {\n          case 'Agent':\n            navigate('/agent/dashboard');\n            break;\n          case 'Supervisor':\n            navigate('/supervisor/dashboard');\n            break;\n          case 'Admin':\n            navigate('/admin/dashboard');\n            break;\n          default:\n            navigate('/');\n        }\n      }\n    },\n    { icon: '👤', label: 'Profile', active: true },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/settings') },\n  ];\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setPasswordData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSaveProfile = async () => {\n    try {\n      if (!user) return;\n      \n      await apiService.updateUser(user.userId, formData);\n      \n      // Update local user state\n      setUser(prev => prev ? { ...prev, ...formData } : null);\n      setEditing(false);\n      \n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile');\n    }\n  };\n\n  const handleChangePassword = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      alert('New passwords do not match');\n      return;\n    }\n    \n    if (passwordData.newPassword.length < 6) {\n      alert('Password must be at least 6 characters long');\n      return;\n    }\n\n    try {\n      // This would be a dedicated change password endpoint\n      alert('Password change functionality would be implemented here');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n      });\n    } catch (error) {\n      console.error('Error changing password:', error);\n      alert('Failed to change password');\n    }\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  if (!user) {\n    return (\n      <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            User profile not found\n          </div>\n        </Card>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"User Profile\" navigationItems={navigationItems}>\n      <ProfileContainer>\n        {/* Profile Summary */}\n        <ProfileCard>\n          <Avatar>\n            {getInitials(user.firstName, user.lastName)}\n          </Avatar>\n          <UserName>{user.firstName} {user.lastName}</UserName>\n          <UserRole>{user.role}</UserRole>\n          <StatusBadge isActive={user.isActive}>\n            {user.isActive ? 'Active' : 'Inactive'}\n          </StatusBadge>\n          \n          <div style={{ marginTop: '20px', width: '100%' }}>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Username:</strong> {user.username}\n            </div>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Email:</strong> {user.email}\n            </div>\n            <div style={{ marginBottom: '10px' }}>\n              <strong>Joined:</strong> {formatDate(user.createdDate)}\n            </div>\n            {user.lastLoginDate && (\n              <div>\n                <strong>Last Login:</strong> {formatDate(user.lastLoginDate)}\n              </div>\n            )}\n          </div>\n\n          {/* User Stats for Agents */}\n          {user.role === 'Agent' && (\n            <StatsGrid>\n              <StatItem>\n                <StatValue>{userStats.totalTasks}</StatValue>\n                <StatLabel>Total Tasks</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.completedTasks}</StatValue>\n                <StatLabel>Completed</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.approvalRate}%</StatValue>\n                <StatLabel>Approval Rate</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{userStats.avgTime}</StatValue>\n                <StatLabel>Avg. Days</StatLabel>\n              </StatItem>\n            </StatsGrid>\n          )}\n        </ProfileCard>\n\n        {/* Profile Form */}\n        <FormContainer>\n          {/* Personal Information */}\n          <FormSection>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>\n              <SectionTitle style={{ marginBottom: 0 }}>Personal Information</SectionTitle>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setEditing(!editing)}\n              >\n                {editing ? 'Cancel' : 'Edit'}\n              </Button>\n            </div>\n\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>\n              <FormGroup>\n                <Label>First Name</Label>\n                <Input\n                  type=\"text\"\n                  name=\"firstName\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  disabled={!editing}\n                />\n              </FormGroup>\n              <FormGroup>\n                <Label>Last Name</Label>\n                <Input\n                  type=\"text\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  disabled={!editing}\n                />\n              </FormGroup>\n            </div>\n\n            <FormGroup>\n              <Label>Email</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                disabled={!editing}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Phone Number</Label>\n              <Input\n                type=\"tel\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                disabled={!editing}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Bio</Label>\n              <TextArea\n                name=\"bio\"\n                value={formData.bio}\n                onChange={handleInputChange}\n                disabled={!editing}\n                placeholder=\"Tell us about yourself...\"\n              />\n            </FormGroup>\n\n            {editing && (\n              <ButtonGroup>\n                <Button variant=\"outline\" onClick={() => setEditing(false)}>\n                  Cancel\n                </Button>\n                <Button onClick={handleSaveProfile}>\n                  Save Changes\n                </Button>\n              </ButtonGroup>\n            )}\n          </FormSection>\n\n          {/* Change Password */}\n          <FormSection>\n            <SectionTitle>Change Password</SectionTitle>\n\n            <FormGroup>\n              <Label>Current Password</Label>\n              <Input\n                type=\"password\"\n                name=\"currentPassword\"\n                value={passwordData.currentPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>New Password</Label>\n              <Input\n                type=\"password\"\n                name=\"newPassword\"\n                value={passwordData.newPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <FormGroup>\n              <Label>Confirm New Password</Label>\n              <Input\n                type=\"password\"\n                name=\"confirmPassword\"\n                value={passwordData.confirmPassword}\n                onChange={handlePasswordChange}\n              />\n            </FormGroup>\n\n            <ButtonGroup>\n              <Button\n                variant=\"secondary\"\n                onClick={handleChangePassword}\n                disabled={!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n              >\n                Change Password\n              </Button>\n            </ButtonGroup>\n          </FormSection>\n        </FormContainer>\n      </ProfileContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAc,2BAA2B;AAC5D,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,gBAAgB,GAAGT,MAAM,CAACU,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,gBAAgB;AAUtB,MAAMG,WAAW,GAAGZ,MAAM,CAACE,IAAI,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GANID,WAAW;AAQjB,MAAME,MAAM,GAAGd,MAAM,CAACU,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAZID,MAAM;AAcZ,MAAME,QAAQ,GAAGhB,MAAM,CAACiB,EAAE;AAC1B;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,QAAQ;AAC/C,CAAC;AAACC,GAAA,GAHIN,QAAQ;AAKd,MAAMO,QAAQ,GAAGvB,MAAM,CAACU,GAAG;AAC3B,WAAWQ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,UAAU;AACjD;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAG1B,MAAM,CAAC2B,IAA2B;AACtD;AACA;AACA;AACA;AACA;AACA,sBAAsBT,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAG,SAAS,GAAG,SAAS;AACrE,WAAWV,KAAK,IAAIA,KAAK,CAACU,QAAQ,GAAG,SAAS,GAAG,SAAS;AAC1D,CAAC;AAACC,GAAA,GARIH,WAAW;AAUjB,MAAMI,aAAa,GAAG9B,MAAM,CAACU,GAAG;AAChC;AACA;AACA,CAAC;AAACqB,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGhC,MAAM,CAACE,IAAI,CAAC;AAChC;AACA,CAAC;AAAC+B,GAAA,GAFID,WAAW;AAIjB,MAAME,YAAY,GAAGlC,MAAM,CAACmC,EAAE;AAC9B;AACA;AACA,6BAA6BjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAClE;AACA,CAAC;AAACC,GAAA,GALIH,YAAY;AAOlB,MAAMI,SAAS,GAAGtC,MAAM,CAACU,GAAG;AAC5B;AACA,CAAC;AAAC6B,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAGxC,MAAM,CAACyC,KAAK;AAC1B;AACA;AACA;AACA,WAAWvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,UAAU;AACjD,CAAC;AAACkB,GAAA,GALIF,KAAK;AAOX,MAAMG,KAAK,GAAG3C,MAAM,CAAC4C,KAAK;AAC1B;AACA;AACA,sBAAsB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,UAAU;AAC5D,mBAAmB3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA,oBAAoB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC4B,OAAO;AACvD;AACA;AACA;AACA;AACA;AACA,wBAAwB9B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAC7D;AACA;AACA,CAAC;AAACa,IAAA,GAjBIN,KAAK;AAmBX,MAAMO,QAAQ,GAAGlD,MAAM,CAACmD,QAAQ;AAChC;AACA;AACA;AACA,sBAAsBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyB,UAAU;AAC5D,mBAAmB3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC4B,OAAO;AACvD;AACA;AACA;AACA,CAAC;AAACI,IAAA,GAdIF,QAAQ;AAgBd,MAAMG,WAAW,GAAGrD,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAAC4C,IAAA,GALID,WAAW;AAOjB,MAAME,SAAS,GAAGvD,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGzD,MAAM,CAACU,GAAG;AAC3B;AACA;AACA,gBAAgBQ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACsC,QAAQ;AACpD,mBAAmBxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2B,YAAY,CAACC,EAAE;AACvD,CAAC;AAACY,IAAA,GALIF,QAAQ;AAOd,MAAMG,SAAS,GAAG5D,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACmD,IAAA,GALID,SAAS;AAOf,MAAME,SAAS,GAAG9D,MAAM,CAACU,GAAG;AAC5B;AACA,WAAWQ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2C,SAAS;AAChD,CAAC;AAACC,IAAA,GAHIF,SAAS;AAKf,MAAMG,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI,EAAEC;EAAY,CAAC,GAAG9D,OAAO,CAAC,CAAC;EACvC,MAAM,CAAC6D,IAAI,EAAEE,OAAO,CAAC,GAAGxE,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACyE,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC;IACvC+E,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC;IAC/CsF,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC;IACzC2F,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG7F,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd+F,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAM1F,UAAU,CAAC2F,cAAc,CAAC,CAAC;MAClD3B,OAAO,CAAC0B,QAAQ,CAAC;MACjBpB,WAAW,CAAC;QACVC,SAAS,EAAEmB,QAAQ,CAACnB,SAAS,IAAI,EAAE;QACnCC,QAAQ,EAAEkB,QAAQ,CAAClB,QAAQ,IAAI,EAAE;QACjCC,KAAK,EAAEiB,QAAQ,CAACjB,KAAK,IAAI,EAAE;QAC3BC,WAAW,EAAEgB,QAAQ,CAAChB,WAAW,IAAI,EAAE;QACvCC,GAAG,EAAE,EAAE,CAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACA,IAAI7B,WAAW,EAAE;QACfC,OAAO,CAACD,WAAW,CAAC;QACpBO,WAAW,CAAC;UACVC,SAAS,EAAER,WAAW,CAACQ,SAAS,IAAI,EAAE;UACtCC,QAAQ,EAAET,WAAW,CAACS,QAAQ,IAAI,EAAE;UACpCC,KAAK,EAAEV,WAAW,CAACU,KAAK,IAAI,EAAE;UAC9BC,WAAW,EAAEX,WAAW,CAACW,WAAW,IAAI,EAAE;UAC1CC,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;IACF,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,IAAI,CAAA1B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+B,IAAI,MAAK,OAAO,EAAE;QACjC,MAAMC,KAAK,GAAG,MAAM/F,UAAU,CAACgG,sBAAsB,CAAC,CAAC;QACvDd,YAAY,CAAC;UACXC,UAAU,EAAEY,KAAK,CAACE,aAAa,IAAI,CAAC;UACpCb,cAAc,EAAEW,KAAK,CAACG,cAAc,IAAI,CAAC;UACzCb,YAAY,EAAE,EAAE;UAAE;UAClBC,OAAO,EAAE,GAAG,CAAE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMO,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,IAAI;IACVhE,KAAK,EAAE,WAAW;IAClBiE,OAAO,EAAEA,CAAA,KAAM;MACb,QAAQtC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+B,IAAI;QACvB,KAAK,OAAO;UACVP,QAAQ,CAAC,kBAAkB,CAAC;UAC5B;QACF,KAAK,YAAY;UACfA,QAAQ,CAAC,uBAAuB,CAAC;UACjC;QACF,KAAK,OAAO;UACVA,QAAQ,CAAC,kBAAkB,CAAC;UAC5B;QACF;UACEA,QAAQ,CAAC,GAAG,CAAC;MACjB;IACF;EACF,CAAC,EACD;IAAEa,IAAI,EAAE,IAAI;IAAEhE,KAAK,EAAE,SAAS;IAAEkE,MAAM,EAAE;EAAK,CAAC,EAC9C;IAAEF,IAAI,EAAE,IAAI;IAAEhE,KAAK,EAAE,UAAU;IAAEiE,OAAO,EAAEA,CAAA,KAAMd,QAAQ,CAAC,WAAW;EAAE,CAAC,CACxE;EAED,MAAMgB,iBAAiB,GAAIC,CAA4D,IAAK;IAC1F,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrC,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,oBAAoB,GAAIL,CAAsC,IAAK;IACvE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC9B,eAAe,CAAC+B,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,IAAI,CAAChD,IAAI,EAAE;MAEX,MAAM9D,UAAU,CAAC+G,UAAU,CAACjD,IAAI,CAACkD,MAAM,EAAE3C,QAAQ,CAAC;;MAElD;MACAL,OAAO,CAAC4C,IAAI,IAAIA,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAE,GAAGvC;MAAS,CAAC,GAAG,IAAI,CAAC;MACvDD,UAAU,CAAC,KAAK,CAAC;MAEjB6C,KAAK,CAAC,+BAA+B,CAAC;IACxC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CqB,KAAK,CAAC,0BAA0B,CAAC;IACnC;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAItC,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MAC7DiC,KAAK,CAAC,4BAA4B,CAAC;MACnC;IACF;IAEA,IAAIrC,YAAY,CAACG,WAAW,CAACoC,MAAM,GAAG,CAAC,EAAE;MACvCF,KAAK,CAAC,6CAA6C,CAAC;MACpD;IACF;IAEA,IAAI;MACF;MACAA,KAAK,CAAC,yDAAyD,CAAC;MAChEpC,eAAe,CAAC;QACdC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDqB,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAC7C,SAAiB,EAAEC,QAAgB,KAAK;IAC3D,OAAO,GAAGD,SAAS,CAAC8C,MAAM,CAAC,CAAC,CAAC,GAAG7C,QAAQ,CAAC6C,MAAM,CAAC,CAAC,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EACpE,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIzD,OAAO,EAAE;IACX,oBACE9D,OAAA,CAACP,eAAe;MAAC+H,KAAK,EAAC,cAAc;MAACxB,eAAe,EAAEA,eAAgB;MAAAyB,QAAA,eACrEzH,OAAA,CAACJ,cAAc;QAAA8H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,IAAI,CAAClE,IAAI,EAAE;IACT,oBACE3D,OAAA,CAACP,eAAe;MAAC+H,KAAK,EAAC,cAAc;MAACxB,eAAe,EAAEA,eAAgB;MAAAyB,QAAA,eACrEzH,OAAA,CAACN,IAAI;QAAA+H,QAAA,eACHzH,OAAA;UAAK8H,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAEtB;EAEA,oBACE7H,OAAA,CAACP,eAAe;IAAC+H,KAAK,EAAC,cAAc;IAACxB,eAAe,EAAEA,eAAgB;IAAAyB,QAAA,eACrEzH,OAAA,CAACC,gBAAgB;MAAAwH,QAAA,gBAEfzH,OAAA,CAACI,WAAW;QAAAqH,QAAA,gBACVzH,OAAA,CAACM,MAAM;UAAAmH,QAAA,EACJR,WAAW,CAACtD,IAAI,CAACS,SAAS,EAAET,IAAI,CAACU,QAAQ;QAAC;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACT7H,OAAA,CAACQ,QAAQ;UAAAiH,QAAA,GAAE9D,IAAI,CAACS,SAAS,EAAC,GAAC,EAACT,IAAI,CAACU,QAAQ;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACrD7H,OAAA,CAACe,QAAQ;UAAA0G,QAAA,EAAE9D,IAAI,CAACgC;QAAI;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAChC7H,OAAA,CAACkB,WAAW;UAACE,QAAQ,EAAEuC,IAAI,CAACvC,QAAS;UAAAqG,QAAA,EAClC9D,IAAI,CAACvC,QAAQ,GAAG,QAAQ,GAAG;QAAU;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEd7H,OAAA;UAAK8H,KAAK,EAAE;YAAEG,SAAS,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,gBAC/CzH,OAAA;YAAK8H,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,gBACnCzH,OAAA;cAAAyH,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,IAAI,CAACyE,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN7H,OAAA;YAAK8H,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,gBACnCzH,OAAA;cAAAyH,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClE,IAAI,CAACW,KAAK;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACN7H,OAAA;YAAK8H,KAAK,EAAE;cAAEK,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,gBACnCzH,OAAA;cAAAyH,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACT,UAAU,CAACzD,IAAI,CAAC0E,WAAW,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,EACLlE,IAAI,CAAC2E,aAAa,iBACjBtI,OAAA;YAAAyH,QAAA,gBACEzH,OAAA;cAAAyH,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACT,UAAU,CAACzD,IAAI,CAAC2E,aAAa,CAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLlE,IAAI,CAACgC,IAAI,KAAK,OAAO,iBACpB3F,OAAA,CAAC+C,SAAS;UAAA0E,QAAA,gBACRzH,OAAA,CAACiD,QAAQ;YAAAwE,QAAA,gBACPzH,OAAA,CAACoD,SAAS;cAAAqE,QAAA,EAAE3C,SAAS,CAACE;YAAU;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C7H,OAAA,CAACsD,SAAS;cAAAmE,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACX7H,OAAA,CAACiD,QAAQ;YAAAwE,QAAA,gBACPzH,OAAA,CAACoD,SAAS;cAAAqE,QAAA,EAAE3C,SAAS,CAACG;YAAc;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjD7H,OAAA,CAACsD,SAAS;cAAAmE,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACX7H,OAAA,CAACiD,QAAQ;YAAAwE,QAAA,gBACPzH,OAAA,CAACoD,SAAS;cAAAqE,QAAA,GAAE3C,SAAS,CAACI,YAAY,EAAC,GAAC;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChD7H,OAAA,CAACsD,SAAS;cAAAmE,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACX7H,OAAA,CAACiD,QAAQ;YAAAwE,QAAA,gBACPzH,OAAA,CAACoD,SAAS;cAAAqE,QAAA,EAAE3C,SAAS,CAACK;YAAO;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1C7H,OAAA,CAACsD,SAAS;cAAAmE,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eAGd7H,OAAA,CAACsB,aAAa;QAAAmG,QAAA,gBAEZzH,OAAA,CAACwB,WAAW;UAAAiG,QAAA,gBACVzH,OAAA;YAAK8H,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEN,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,gBAC3GzH,OAAA,CAAC0B,YAAY;cAACoG,KAAK,EAAE;gBAAEK,YAAY,EAAE;cAAE,CAAE;cAAAV,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eAC7E7H,OAAA,CAACL,MAAM;cACL+I,OAAO,EAAC,SAAS;cACjBC,IAAI,EAAC,IAAI;cACTzC,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAAC,CAACD,OAAO,CAAE;cAAAyD,QAAA,EAEnCzD,OAAO,GAAG,QAAQ,GAAG;YAAM;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7H,OAAA;YAAK8H,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEK,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAApB,QAAA,gBAC3EzH,OAAA,CAAC8B,SAAS;cAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;gBAAAyF,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzB7H,OAAA,CAACmC,KAAK;gBACJ2G,IAAI,EAAC,MAAM;gBACXxC,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAErC,QAAQ,CAACE,SAAU;gBAC1B2E,QAAQ,EAAE3C,iBAAkB;gBAC5B4C,QAAQ,EAAE,CAAChF;cAAQ;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ7H,OAAA,CAAC8B,SAAS;cAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;gBAAAyF,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxB7H,OAAA,CAACmC,KAAK;gBACJ2G,IAAI,EAAC,MAAM;gBACXxC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAErC,QAAQ,CAACG,QAAS;gBACzB0E,QAAQ,EAAE3C,iBAAkB;gBAC5B4C,QAAQ,EAAE,CAAChF;cAAQ;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpB7H,OAAA,CAACmC,KAAK;cACJ2G,IAAI,EAAC,OAAO;cACZxC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAErC,QAAQ,CAACI,KAAM;cACtByE,QAAQ,EAAE3C,iBAAkB;cAC5B4C,QAAQ,EAAE,CAAChF;YAAQ;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3B7H,OAAA,CAACmC,KAAK;cACJ2G,IAAI,EAAC,KAAK;cACVxC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAErC,QAAQ,CAACK,WAAY;cAC5BwE,QAAQ,EAAE3C,iBAAkB;cAC5B4C,QAAQ,EAAE,CAAChF;YAAQ;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClB7H,OAAA,CAAC0C,QAAQ;cACP4D,IAAI,EAAC,KAAK;cACVC,KAAK,EAAErC,QAAQ,CAACM,GAAI;cACpBuE,QAAQ,EAAE3C,iBAAkB;cAC5B4C,QAAQ,EAAE,CAAChF,OAAQ;cACnBiF,WAAW,EAAC;YAA2B;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,EAEX7D,OAAO,iBACNhE,OAAA,CAAC6C,WAAW;YAAA4E,QAAA,gBACVzH,OAAA,CAACL,MAAM;cAAC+I,OAAO,EAAC,SAAS;cAACxC,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAAC,KAAK,CAAE;cAAAwD,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7H,OAAA,CAACL,MAAM;cAACuG,OAAO,EAAES,iBAAkB;cAAAc,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eAGd7H,OAAA,CAACwB,WAAW;UAAAiG,QAAA,gBACVzH,OAAA,CAAC0B,YAAY;YAAA+F,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAE5C7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/B7H,OAAA,CAACmC,KAAK;cACJ2G,IAAI,EAAC,UAAU;cACfxC,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAE9B,YAAY,CAACE,eAAgB;cACpCoE,QAAQ,EAAErC;YAAqB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3B7H,OAAA,CAACmC,KAAK;cACJ2G,IAAI,EAAC,UAAU;cACfxC,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAE9B,YAAY,CAACG,WAAY;cAChCmE,QAAQ,EAAErC;YAAqB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ7H,OAAA,CAAC8B,SAAS;YAAA2F,QAAA,gBACRzH,OAAA,CAACgC,KAAK;cAAAyF,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnC7H,OAAA,CAACmC,KAAK;cACJ2G,IAAI,EAAC,UAAU;cACfxC,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAE9B,YAAY,CAACI,eAAgB;cACpCkE,QAAQ,EAAErC;YAAqB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ7H,OAAA,CAAC6C,WAAW;YAAA4E,QAAA,eACVzH,OAAA,CAACL,MAAM;cACL+I,OAAO,EAAC,WAAW;cACnBxC,OAAO,EAAEa,oBAAqB;cAC9BiC,QAAQ,EAAE,CAACvE,YAAY,CAACE,eAAe,IAAI,CAACF,YAAY,CAACG,WAAW,IAAI,CAACH,YAAY,CAACI,eAAgB;cAAA4C,QAAA,EACvG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAACnE,EAAA,CAnXID,WAAqB;EAAA,QACK3D,OAAO,EAsBpBP,WAAW;AAAA;AAAA2J,IAAA,GAvBxBzF,WAAqB;AAqX3B,eAAeA,WAAW;AAAC,IAAAtD,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAO,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAA0F,IAAA;AAAAC,YAAA,CAAAhJ,EAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}