import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem } from '../../services/apiService';

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const PriorityBadge = styled.span<{ priority: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.priority) {
      case 'high':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      case 'medium':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'low':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const ReviewModal = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'flex' : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
`;

const ModalTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
`;

const SupervisorReview: React.FC = () => {
  const [reviewTasks, setReviewTasks] = useState<LeadListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('pending-review');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLead, setSelectedLead] = useState<LeadListItem | null>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadReviewTasks();
  }, [statusFilter]);

  const loadReviewTasks = async () => {
    try {
      setLoading(true);
      const response = await apiService.getLeads(1, 100, statusFilter === 'all' ? undefined : statusFilter);
      setReviewTasks(response.data || []);
    } catch (error) {
      console.error('Error loading review tasks:', error);
      // Mock data for demo
      setReviewTasks([
        {
          leadId: 5,
          customerName: 'Charlie Brown',
          mobileNumber: '9876543214',
          loanType: 'Home Loan',
          status: 'pending-review',
          createdDate: '2024-01-12T09:00:00Z',
          assignedDate: '2024-01-12T10:00:00Z',
          submittedDate: '2024-01-15T17:30:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Agent Smith',
          documentCount: 4,
          croppedImageCount: 3,
        },
        {
          leadId: 6,
          customerName: 'Diana Prince',
          mobileNumber: '9876543215',
          loanType: 'Car Loan',
          status: 'pending-review',
          createdDate: '2024-01-13T11:15:00Z',
          assignedDate: '2024-01-13T12:00:00Z',
          submittedDate: '2024-01-16T09:45:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Agent Johnson',
          documentCount: 3,
          croppedImageCount: 2,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filteredTasks = reviewTasks.filter(task =>
    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.mobileNumber.includes(searchTerm) ||
    task.loanType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (task.assignedToName && task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/supervisor/dashboard') },
    { icon: '👁️', label: 'Review Queue', active: true },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/supervisor/reports') },
    { icon: '👥', label: 'Team', onClick: () => navigate('/supervisor/team') },
  ];

  const handleReviewAction = (lead: LeadListItem, action: 'approve' | 'reject') => {
    setSelectedLead(lead);
    setReviewAction(action);
    setReviewComments('');
    setRejectionReason('');
  };

  const submitReview = async () => {
    if (!selectedLead || !reviewAction) return;

    try {
      const newStatus = reviewAction === 'approve' ? 'approved' : 'rejected';
      await apiService.updateLeadStatus(
        selectedLead.leadId,
        newStatus,
        reviewComments,
        reviewAction === 'reject' ? rejectionReason : undefined
      );

      // Update local state
      setReviewTasks(tasks => 
        tasks.map(task => 
          task.leadId === selectedLead.leadId 
            ? { ...task, status: newStatus }
            : task
        )
      );

      // Close modal
      setSelectedLead(null);
      setReviewAction(null);
      
      // Reload if filtering by status
      if (statusFilter !== 'all') {
        loadReviewTasks();
      }

    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getPriority = (submittedDate: string) => {
    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff > 2) return 'high';
    if (daysDiff > 1) return 'medium';
    return 'low';
  };

  const calculateWaitTime = (submittedDate: string) => {
    const daysDiff = Math.floor((Date.now() - new Date(submittedDate).getTime()) / (1000 * 60 * 60 * 24));
    return `${daysDiff} day${daysDiff !== 1 ? 's' : ''}`;
  };

  if (loading) {
    return (
      <DashboardLayout title="Review Queue" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Review Queue" navigationItems={navigationItems}>
      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Pending Reviews</h2>

        <FilterContainer>
          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
            <option value="pending-review">Pending Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="all">All Reviews</option>
          </FilterSelect>
          
          <SearchInput
            type="text"
            placeholder="Search by customer, mobile, loan type, or agent..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </FilterContainer>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Agent</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Priority</TableHeader>
                <TableHeader>Submitted</TableHeader>
                <TableHeader>Wait Time</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {filteredTasks.map((task) => (
                <TableRow key={task.leadId}>
                  <TableCell>{task.customerName}</TableCell>
                  <TableCell>{task.mobileNumber}</TableCell>
                  <TableCell>{task.loanType}</TableCell>
                  <TableCell>{task.assignedToName || '-'}</TableCell>
                  <TableCell>
                    <StatusBadge status={task.status}>
                      {task.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    {task.submittedDate && (
                      <PriorityBadge priority={getPriority(task.submittedDate)}>
                        {getPriority(task.submittedDate).toUpperCase()}
                      </PriorityBadge>
                    )}
                  </TableCell>
                  <TableCell>
                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}
                  </TableCell>
                  <TableCell>
                    {task.submittedDate ? calculateWaitTime(task.submittedDate) : '-'}
                  </TableCell>
                  <TableCell>
                    <ActionButtons>
                      <Button
                        size="sm"
                        onClick={() => navigate(`/lead/${task.leadId}`)}
                      >
                        View
                      </Button>
                      {task.status === 'pending-review' && (
                        <>
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleReviewAction(task, 'approve')}
                          >
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleReviewAction(task, 'reject')}
                          >
                            Reject
                          </Button>
                        </>
                      )}
                    </ActionButtons>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {filteredTasks.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            {searchTerm ? 'No tasks found matching your search.' : 'No tasks pending review.'}
          </div>
        )}
      </Card>

      {/* Review Modal */}
      <ReviewModal isOpen={!!selectedLead && !!reviewAction}>
        <ModalContent>
          <ModalTitle>
            {reviewAction === 'approve' ? 'Approve' : 'Reject'} Lead - {selectedLead?.customerName}
          </ModalTitle>
          
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
              Comments:
            </label>
            <TextArea
              value={reviewComments}
              onChange={(e) => setReviewComments(e.target.value)}
              placeholder="Add your review comments..."
            />
          </div>

          {reviewAction === 'reject' && (
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                Rejection Reason:
              </label>
              <FilterSelect
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                style={{ width: '100%' }}
              >
                <option value="">Select reason...</option>
                <option value="incomplete-documents">Incomplete Documents</option>
                <option value="poor-quality">Poor Quality Images</option>
                <option value="verification-failed">Verification Failed</option>
                <option value="incorrect-information">Incorrect Information</option>
                <option value="other">Other</option>
              </FilterSelect>
            </div>
          )}

          <ModalActions>
            <Button
              variant="outline"
              onClick={() => {
                setSelectedLead(null);
                setReviewAction(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant={reviewAction === 'approve' ? 'secondary' : 'danger'}
              onClick={submitReview}
              disabled={!reviewComments || (reviewAction === 'reject' && !rejectionReason)}
            >
              {reviewAction === 'approve' ? 'Approve' : 'Reject'}
            </Button>
          </ModalActions>
        </ModalContent>
      </ReviewModal>
    </DashboardLayout>
  );
};

export default SupervisorReview;
