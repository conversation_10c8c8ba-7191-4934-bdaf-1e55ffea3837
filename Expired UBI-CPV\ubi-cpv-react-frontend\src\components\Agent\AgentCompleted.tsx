import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, LeadListItem } from '../../services/apiService';

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const StatCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px;
`;

const StatValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: ${props => props.theme.colors.textDark};
`;

const StatLabel = styled.div`
  font-size: 14px;
  color: ${props => props.theme.colors.textLight};
  font-weight: 500;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  background: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: 8px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  background-color: ${props => props.theme.colors.offWhite};
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
`;

const TableCell = styled.td`
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
`;

const TableRow = styled.tr`
  &:hover {
    background-color: ${props => props.theme.colors.lightGray};
  }
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'approved':
      case 'completed':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const AgentCompleted: React.FC = () => {
  const [completedTasks, setCompletedTasks] = useState<LeadListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({
    totalCompleted: 0,
    approved: 0,
    rejected: 0,
    pendingReview: 0,
  });
  const navigate = useNavigate();

  useEffect(() => {
    loadCompletedTasks();
  }, [statusFilter]);

  const loadCompletedTasks = async () => {
    try {
      setLoading(true);
      
      // Load completed tasks based on filter
      const statuses = statusFilter === 'all' 
        ? ['approved', 'rejected', 'pending-review'] 
        : [statusFilter];
      
      const allTasks: LeadListItem[] = [];
      
      for (const status of statuses) {
        try {
          const response = await apiService.getLeads(1, 100, status);
          allTasks.push(...(response.data || []));
        } catch (error) {
          console.error(`Error loading ${status} tasks:`, error);
        }
      }
      
      setCompletedTasks(allTasks);
      
      // Calculate stats
      const approved = allTasks.filter(task => task.status === 'approved').length;
      const rejected = allTasks.filter(task => task.status === 'rejected').length;
      const pendingReview = allTasks.filter(task => task.status === 'pending-review').length;
      
      setStats({
        totalCompleted: allTasks.length,
        approved,
        rejected,
        pendingReview,
      });
      
    } catch (error) {
      console.error('Error loading completed tasks:', error);
      // Mock data for demo
      const mockTasks = [
        {
          leadId: 3,
          customerName: 'Alice Johnson',
          mobileNumber: '9876543212',
          loanType: 'Car Loan',
          status: 'approved',
          createdDate: '2024-01-10T08:00:00Z',
          assignedDate: '2024-01-10T09:00:00Z',
          submittedDate: '2024-01-12T16:30:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 3,
          croppedImageCount: 2,
        },
        {
          leadId: 4,
          customerName: 'Bob Wilson',
          mobileNumber: '9876543213',
          loanType: 'Personal Loan',
          status: 'rejected',
          createdDate: '2024-01-08T10:15:00Z',
          assignedDate: '2024-01-08T11:00:00Z',
          submittedDate: '2024-01-09T14:20:00Z',
          createdByName: 'Admin User',
          assignedToName: 'Current Agent',
          documentCount: 2,
          croppedImageCount: 1,
        },
      ];
      
      setCompletedTasks(mockTasks);
      setStats({
        totalCompleted: 2,
        approved: 1,
        rejected: 1,
        pendingReview: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredTasks = completedTasks.filter(task =>
    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.mobileNumber.includes(searchTerm) ||
    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const navigationItems = [
    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },
    { icon: '📋', label: 'My Tasks', onClick: () => navigate('/agent/tasks') },
    { icon: '✅', label: 'Completed', active: true },
    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
  };

  if (loading) {
    return (
      <DashboardLayout title="Completed Tasks" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Completed Tasks" navigationItems={navigationItems}>
      {/* Stats Cards */}
      <StatsContainer>
        <StatCard>
          <StatValue>{stats.totalCompleted}</StatValue>
          <StatLabel>Total Completed</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue style={{ color: '#2e7d32' }}>{stats.approved}</StatValue>
          <StatLabel>Approved</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue style={{ color: '#c62828' }}>{stats.rejected}</StatValue>
          <StatLabel>Rejected</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue style={{ color: '#4a148c' }}>{stats.pendingReview}</StatValue>
          <StatLabel>Pending Review</StatLabel>
        </StatCard>
      </StatsContainer>

      <Card>
        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Completed Verifications</h2>

        <FilterContainer>
          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>
            <option value="all">All Completed</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="pending-review">Pending Review</option>
          </FilterSelect>
          
          <SearchInput
            type="text"
            placeholder="Search by customer name, mobile, or loan type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </FilterContainer>

        <TableContainer>
          <Table>
            <thead>
              <tr>
                <TableHeader>Customer</TableHeader>
                <TableHeader>Mobile</TableHeader>
                <TableHeader>Loan Type</TableHeader>
                <TableHeader>Status</TableHeader>
                <TableHeader>Submitted Date</TableHeader>
                <TableHeader>Duration</TableHeader>
                <TableHeader>Actions</TableHeader>
              </tr>
            </thead>
            <tbody>
              {filteredTasks.map((task) => (
                <TableRow key={task.leadId}>
                  <TableCell>{task.customerName}</TableCell>
                  <TableCell>{task.mobileNumber}</TableCell>
                  <TableCell>{task.loanType}</TableCell>
                  <TableCell>
                    <StatusBadge status={task.status}>
                      {task.status.replace('-', ' ').toUpperCase()}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    {task.submittedDate ? formatDate(task.submittedDate) : '-'}
                  </TableCell>
                  <TableCell>
                    {task.assignedDate && task.submittedDate 
                      ? calculateDuration(task.assignedDate, task.submittedDate)
                      : '-'
                    }
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      onClick={() => navigate(`/lead/${task.leadId}`)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {filteredTasks.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>
            {searchTerm ? 'No completed tasks found matching your search.' : 'No completed tasks yet.'}
          </div>
        )}
      </Card>
    </DashboardLayout>
  );
};

export default AgentCompleted;
