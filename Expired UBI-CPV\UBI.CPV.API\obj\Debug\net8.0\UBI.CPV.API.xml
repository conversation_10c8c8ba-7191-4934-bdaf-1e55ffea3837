<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UBI.CPV.API</name>
    </assembly>
    <members>
        <member name="T:UBI.CPV.API.Controllers.AuthController">
            <summary>
            Authentication controller for user login, logout, and token management
            </summary>
        </member>
        <member name="M:UBI.CPV.API.Controllers.AuthController.Login(UBI.CPV.API.Models.DTOs.LoginRequestDto)">
            <summary>
            Authenticates a user and returns a JWT token
            </summary>
            <param name="request">Login credentials including username, password, and role</param>
            <returns>Login response with JWT token and user information</returns>
            <response code="200">Login successful or failed with appropriate message</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:UBI.CPV.API.Controllers.AuthController.Logout">
            <summary>
            Logs out the current user by invalidating their session
            </summary>
            <returns>Logout confirmation</returns>
            <response code="200">Logout successful</response>
            <response code="401">Unauthorized - invalid or missing token</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:UBI.CPV.API.Controllers.AuthController.RefreshToken(UBI.CPV.API.Models.DTOs.RefreshTokenDto)">
            <summary>
            Refreshes an expired JWT token using a refresh token
            </summary>
            <param name="request">Refresh token request containing the current token and refresh token</param>
            <returns>New JWT token and refresh token</returns>
            <response code="200">Token refresh successful or failed with appropriate message</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:UBI.CPV.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Gets the current authenticated user's information
            </summary>
            <returns>Current user information</returns>
            <response code="200">User information retrieved successfully</response>
            <response code="401">Unauthorized - invalid or missing token</response>
            <response code="404">User not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="T:UBI.CPV.API.Controllers.LeadsController">
            <summary>
            Lead management controller for creating, updating, and retrieving customer verification leads
            </summary>
        </member>
        <member name="M:UBI.CPV.API.Controllers.LeadsController.GetLeads(System.Int32,System.Int32,System.String,System.Nullable{System.Int32})">
            <summary>
            Retrieves a paginated list of leads with optional filtering
            </summary>
            <param name="pageNumber">Page number (default: 1)</param>
            <param name="pageSize">Number of items per page (default: 10)</param>
            <param name="status">Filter by lead status (optional)</param>
            <param name="assignedTo">Filter by assigned agent ID (optional)</param>
            <returns>Paginated list of leads</returns>
            <response code="200">Leads retrieved successfully</response>
            <response code="401">Unauthorized - invalid or missing token</response>
            <response code="500">Internal server error</response>
        </member>
    </members>
</doc>
