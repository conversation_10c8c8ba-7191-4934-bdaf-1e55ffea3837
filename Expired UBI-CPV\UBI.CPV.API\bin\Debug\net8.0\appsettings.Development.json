{"ConnectionStrings": {"DefaultConnection": "Data Source=MBSDEVGGN63729A;Initial Catalog=UBI_CPV_DB;User ID=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true;Encrypt=false;"}, "Jwt": {"Key": "development-jwt-key-that-is-at-least-32-characters-long-for-dev-only", "Issuer": "UBI-CPV-API-Dev", "Audience": "UBI-CPV-Client-Dev"}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}