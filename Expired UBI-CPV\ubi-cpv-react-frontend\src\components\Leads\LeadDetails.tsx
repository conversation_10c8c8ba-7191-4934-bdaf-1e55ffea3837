import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Card, Button } from '../../styles/GlobalStyles';
import { apiService, Lead } from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid ${props => props.theme.colors.mediumGray};
`;

const Title = styled.h1`
  font-size: 24px;
  font-weight: 600;
  color: ${props => props.theme.colors.primary};
`;

const BackButton = styled(Button)`
  margin-right: 20px;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const InfoItem = styled.div`
  margin-bottom: 15px;
`;

const InfoLabel = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.textMedium};
  margin-bottom: 5px;
  font-size: 14px;
`;

const InfoValue = styled.div`
  color: ${props => props.theme.colors.textDark};
  font-size: 16px;
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;

  ${props => {
    switch (props.status) {
      case 'new':
        return `
          background-color: #e3f2fd;
          color: #0d47a1;
        `;
      case 'assigned':
        return `
          background-color: #fff3e0;
          color: #e65100;
        `;
      case 'in-progress':
        return `
          background-color: #fff8e1;
          color: #ff8f00;
        `;
      case 'pending-review':
        return `
          background-color: #f3e5f5;
          color: #4a148c;
        `;
      case 'approved':
        return `
          background-color: #e8f5e9;
          color: #2e7d32;
        `;
      case 'rejected':
        return `
          background-color: #ffebee;
          color: #c62828;
        `;
      default:
        return `
          background-color: #f5f5f5;
          color: #666;
        `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const DocumentsSection = styled.div`
  margin-top: 20px;
`;

const DocumentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
`;

const DocumentCard = styled.div`
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: 15px;
  text-align: center;
  background: ${props => props.theme.colors.white};
`;

const DocumentIcon = styled.div`
  font-size: 32px;
  margin-bottom: 10px;
`;

const DocumentName = styled.div`
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
`;

const DocumentDate = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textLight};
`;

const LeadDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadLeadDetails(parseInt(id));
    }
  }, [id]);

  const loadLeadDetails = async (leadId: number) => {
    try {
      setLoading(true);
      const leadData = await apiService.getLead(leadId);
      setLead(leadData);
    } catch (error) {
      console.error('Error loading lead details:', error);
      // Mock data for demo
      setLead({
        leadId: leadId,
        customerName: 'John Doe',
        mobileNumber: '9876543210',
        loanType: 'Personal Loan',
        status: 'assigned',
        createdDate: '2024-01-15T10:30:00Z',
        assignedDate: '2024-01-15T11:00:00Z',
        addresses: [],
        statusHistory: [],
        documents: [],
        croppedImages: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (user?.role === 'Agent') {
      navigate('/agent/dashboard');
    } else if (user?.role === 'Supervisor') {
      navigate('/supervisor/dashboard');
    } else if (user?.role === 'Admin') {
      navigate('/admin/dashboard');
    }
  };

  const handleStartVerification = () => {
    navigate(`/lead/${id}/verification`);
  };

  const handleUploadDocuments = () => {
    navigate(`/lead/${id}/documents`);
  };

  const handleUpdateStatus = async (newStatus: string, comments?: string, rejectionReason?: string) => {
    if (!lead) return;

    try {
      await apiService.updateLeadStatus(lead.leadId, newStatus, comments, rejectionReason);
      setLead({ ...lead, status: newStatus });

      // Show success message
      alert(`Lead status updated to ${newStatus.replace('-', ' ')}`);
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update status');
    }
  };

  const handleAssignLead = async (agentId: number) => {
    if (!lead) return;

    try {
      await apiService.assignLead(lead.leadId, agentId, 'Lead reassigned');
      // Reload lead details to get updated assignment info
      loadLeadDetails(lead.leadId);
      alert('Lead assigned successfully');
    } catch (error) {
      console.error('Error assigning lead:', error);
      alert('Failed to assign lead');
    }
  };

  const handleDownloadDocuments = () => {
    // This would implement document download functionality
    alert('Document download functionality would be implemented here');
  };

  const handlePrintLead = () => {
    window.print();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Container>
        <div>Loading lead details...</div>
      </Container>
    );
  }

  if (!lead) {
    return (
      <Container>
        <div>Lead not found</div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <BackButton variant="outline" onClick={handleBack}>
            ← Back
          </BackButton>
          <Title>Lead Details - {lead.customerName}</Title>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <StatusBadge status={lead.status}>
            {lead.status.replace('-', ' ').toUpperCase()}
          </StatusBadge>
          <Button variant="outline" size="sm" onClick={handlePrintLead}>
            🖨️ Print
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownloadDocuments}>
            📥 Download
          </Button>
        </div>
      </Header>

      <InfoGrid>
        <Card>
          <h3 style={{ marginBottom: '15px', color: '#007E3A' }}>Customer Information</h3>
          <InfoItem>
            <InfoLabel>Customer Name</InfoLabel>
            <InfoValue>{lead.customerName}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Mobile Number</InfoLabel>
            <InfoValue>{lead.mobileNumber}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Loan Type</InfoLabel>
            <InfoValue>{lead.loanType}</InfoValue>
          </InfoItem>
        </Card>

        <Card>
          <h3 style={{ marginBottom: '15px', color: '#007E3A' }}>Lead Timeline</h3>
          <InfoItem>
            <InfoLabel>Created Date</InfoLabel>
            <InfoValue>{formatDate(lead.createdDate)}</InfoValue>
          </InfoItem>
          {lead.assignedDate && (
            <InfoItem>
              <InfoLabel>Assigned Date</InfoLabel>
              <InfoValue>{formatDate(lead.assignedDate)}</InfoValue>
            </InfoItem>
          )}
          {lead.startedDate && (
            <InfoItem>
              <InfoLabel>Started Date</InfoLabel>
              <InfoValue>{formatDate(lead.startedDate)}</InfoValue>
            </InfoItem>
          )}
          {lead.submittedDate && (
            <InfoItem>
              <InfoLabel>Submitted Date</InfoLabel>
              <InfoValue>{formatDate(lead.submittedDate)}</InfoValue>
            </InfoItem>
          )}
        </Card>
      </InfoGrid>

      {/* Action Buttons */}
      <Card>
        <h3 style={{ marginBottom: '15px', color: '#007E3A' }}>Actions</h3>
        <ActionButtons>
          {user?.role === 'Agent' && lead.status === 'assigned' && (
            <Button onClick={() => handleUpdateStatus('in-progress')}>
              Start Verification
            </Button>
          )}

          {user?.role === 'Agent' && lead.status === 'in-progress' && (
            <>
              <Button onClick={handleStartVerification}>
                Continue Verification
              </Button>
              <Button variant="secondary" onClick={handleUploadDocuments}>
                Upload Documents
              </Button>
              <Button variant="outline" onClick={() => handleUpdateStatus('pending-review')}>
                Submit for Review
              </Button>
            </>
          )}

          {user?.role === 'Supervisor' && lead.status === 'pending-review' && (
            <>
              <Button onClick={() => handleUpdateStatus('approved', 'Approved by supervisor')}>
                ✅ Approve
              </Button>
              <Button variant="danger" onClick={() => handleUpdateStatus('rejected', 'Rejected by supervisor', 'verification-failed')}>
                ❌ Reject
              </Button>
            </>
          )}

          {user?.role === 'Admin' && (
            <>
              {lead.status === 'new' && (
                <Button variant="secondary" onClick={() => alert('Assignment functionality would open a modal')}>
                  👤 Assign Agent
                </Button>
              )}
              <Button variant="outline" onClick={() => navigate(`/admin/leads`)}>
                📋 View All Leads
              </Button>
            </>
          )}

          {(user?.role === 'Supervisor' || user?.role === 'Admin') && (
            <Button variant="outline" onClick={() => alert('Lead history modal would open here')}>
              📊 View History
            </Button>
          )}
        </ActionButtons>
      </Card>

      {/* Documents Section */}
      <Card>
        <DocumentsSection>
          <h3 style={{ marginBottom: '15px', color: '#007E3A' }}>Documents</h3>
          <DocumentGrid>
            <DocumentCard>
              <DocumentIcon>📄</DocumentIcon>
              <DocumentName>ID Proof</DocumentName>
              <DocumentDate>Not uploaded</DocumentDate>
            </DocumentCard>
            <DocumentCard>
              <DocumentIcon>🏠</DocumentIcon>
              <DocumentName>Address Proof</DocumentName>
              <DocumentDate>Not uploaded</DocumentDate>
            </DocumentCard>
            <DocumentCard>
              <DocumentIcon>💼</DocumentIcon>
              <DocumentName>Income Proof</DocumentName>
              <DocumentDate>Not uploaded</DocumentDate>
            </DocumentCard>
          </DocumentGrid>
        </DocumentsSection>
      </Card>
    </Container>
  );
};

export default LeadDetails;
