2025-05-29 18:04:59.335 +05:30 [ERR] An error occurred while creating the database or seeding data
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 203
2025-05-29 18:04:59.532 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 18:04:59.829 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 18:05:00.522 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 18:05:00.548 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 18:05:00.563 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 18:05:00.578 +05:30 [INF] Hosting environment: Development
2025-05-29 18:05:00.584 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-29 18:05:21.397 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger - null null
2025-05-29 18:05:21.773 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger - 301 0 null 370.9317ms
2025-05-29 18:05:21.818 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 18:05:21.918 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 99.2274ms
2025-05-29 18:05:21.973 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null
2025-05-29 18:05:22.015 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-29 18:05:22.026 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null
2025-05-29 18:05:22.026 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null
2025-05-29 18:05:22.027 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null
2025-05-29 18:05:22.062 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 88.5761ms
2025-05-29 18:05:22.085 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-29 18:05:22.095 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-29 18:05:22.131 +05:30 [INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css'
2025-05-29 18:05:22.132 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 106.2456ms
2025-05-29 18:05:22.133 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 105.56ms
2025-05-29 18:05:22.167 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 140.9531ms
2025-05-29 18:05:22.565 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 18:05:22.590 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null
2025-05-29 18:05:22.628 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-29 18:05:22.644 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 54.5137ms
2025-05-29 18:05:22.806 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 240.5526ms
[2025-05-29 18:22:30.776 +05:30 ERR] An error occurred while creating the database or seeding data {}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 203
[2025-05-29 18:22:30.938 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-29 18:22:31.075 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-29 18:22:31.785 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:22:31.795 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:22:31.896 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:22:31.904 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:22:31.912 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:22:33.446 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PC:********","RequestPath":"/","ConnectionId":"0HNCULUML50PC"}
[2025-05-29 18:22:34.197 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 770.251ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PC:********","RequestPath":"/","ConnectionId":"0HNCULUML50PC"}
[2025-05-29 18:22:34.228 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PC:********","RequestPath":"/","ConnectionId":"0HNCULUML50PC"}
[2025-05-29 18:22:42.451 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PF:********","RequestPath":"/swagger/index.html","ConnectionId":"0HNCULUML50PF"}
[2025-05-29 18:22:42.559 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 108.4392ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PF:********","RequestPath":"/swagger/index.html","ConnectionId":"0HNCULUML50PF"}
[2025-05-29 18:22:42.636 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PF:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULUML50PF"}
[2025-05-29 18:22:42.745 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PM:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULUML50PM"}
[2025-05-29 18:22:42.738 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PL:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULUML50PL"}
[2025-05-29 18:22:42.738 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:42.738 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PN:********","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ConnectionId":"0HNCULUML50PN"}
[2025-05-29 18:22:42.759 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/_vs/browserLink - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PP:********","RequestPath":"/_vs/browserLink","ConnectionId":"0HNCULUML50PP"}
[2025-05-29 18:22:42.798 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULUML50PF:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULUML50PF"}
[2025-05-29 18:22:42.925 +05:30 INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULUML50PL:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULUML50PL"}
[2025-05-29 18:22:42.979 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 343.4249ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PF:********","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULUML50PF"}
[2025-05-29 18:22:43.015 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/_framework/aspnetcore-browser-refresh.js - 200 13774 application/javascript; charset=utf-8 277.2607ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PN:********","RequestPath":"/_framework/aspnetcore-browser-refresh.js","ConnectionId":"0HNCULUML50PN"}
[2025-05-29 18:22:42.930 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULUML50PM:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULUML50PM"}
[2025-05-29 18:22:43.064 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:43.069 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 331.0694ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PL:********","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULUML50PL"}
[2025-05-29 18:22:43.171 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 433.4435ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:43.180 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 435.9329ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PM:********","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULUML50PM"}
[2025-05-29 18:22:43.185 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/_vs/browserLink - 200 null text/javascript; charset=UTF-8 426.1271ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PP:********","RequestPath":"/_vs/browserLink","ConnectionId":"0HNCULUML50PP"}
[2025-05-29 18:22:43.331 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PP:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCULUML50PP"}
[2025-05-29 18:22:43.350 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:43.384 +05:30 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:43.410 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 59.3969ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PO:********","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULUML50PO"}
[2025-05-29 18:22:43.424 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 92.7665ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PP:********","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCULUML50PP"}
[2025-05-29 18:23:19.122 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 71 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:23:19.153 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:23:19.166 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:23:19.188 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:23:19.244 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:23:39.575 +05:30 ERR] An exception occurred while iterating over the results of a query for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync() {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-05-29 18:24:22.724 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 55
[2025-05-29 18:24:22.819 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:22.885 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 63593.3021ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:22.915 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:22.942 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 63820.3063ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:29.444 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:59358/api/Auth/login - application/json 71 {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:29.488 +05:30 INF] CORS policy execution failed. {"EventId":{"Id":5,"Name":"PolicyFailure"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:29.510 +05:30 INF] Request origin https://localhost:59358 does not have permission to access the resource. {"EventId":{"Id":6,"Name":"OriginNotAllowed"},"SourceContext":"Microsoft.AspNetCore.Cors.Infrastructure.CorsService","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:29.536 +05:30 INF] Executing endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Name":"ExecutingEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:29.575 +05:30 INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[UBI.CPV.API.Models.DTOs.LoginResponseDto]] Login(UBI.CPV.API.Models.DTOs.LoginRequestDto) on controller UBI.CPV.API.Controllers.AuthController (UBI.CPV.API). {"EventId":{"Id":102,"Name":"ControllerActionExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:37.212 +05:30 ERR] An exception occurred while iterating over the results of a query for context type 'UBI.CPV.API.Data.ApplicationDbContext'.
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync() {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-05-29 18:24:45.612 +05:30 ERR] Error during login for user: admin {"SourceContext":"UBI.CPV.API.Controllers.AuthController","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetPoolGroup(String connectionString)
   at Microsoft.Data.Sqlite.SqliteConnection.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnection..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteRelationalConnection.CreateDbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.get_DbConnection()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at UBI.CPV.API.Controllers.AuthController.Login(LoginRequestDto request) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Controllers\AuthController.cs:line 55
[2025-05-29 18:24:45.668 +05:30 INF] Executing ObjectResult, writing value of type 'UBI.CPV.API.Models.DTOs.LoginResponseDto'. {"EventId":{"Id":1,"Name":"ObjectResultExecuting"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor","ActionId":"d0f88b87-1c46-4178-ac07-503a839c46d4","ActionName":"UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:45.710 +05:30 INF] Executed action UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API) in 16076.5813ms {"EventId":{"Id":105,"Name":"ActionExecuted"},"SourceContext":"Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:45.723 +05:30 INF] Executed endpoint 'UBI.CPV.API.Controllers.AuthController.Login (UBI.CPV.API)' {"EventId":{"Id":1,"Name":"ExecutedEndpoint"},"SourceContext":"Microsoft.AspNetCore.Routing.EndpointMiddleware","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
[2025-05-29 18:24:45.736 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:59358/api/Auth/login - 500 null application/json; charset=utf-8 16291.5976ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULUML50PR:********","RequestPath":"/api/Auth/login","ConnectionId":"0HNCULUML50PR"}
