{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Admin\\\\AdminReports.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c = ReportsContainer;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c2 = StatsGrid;\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n_c3 = StatCard;\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n_c4 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 12px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n_c5 = StatLabel;\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c6 = ChartContainer;\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n_c7 = ChartCard;\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n_c8 = ChartTitle;\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c9 = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c0 = FilterSelect;\nconst MetricsTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c1 = MetricsTable;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c10 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c11 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c12 = TableRow;\nconst TrendIndicator = styled.span`\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ${props => {\n  switch (props.trend) {\n    case 'up':\n      return `color: #2e7d32;`;\n    case 'down':\n      return `color: #c62828;`;\n    case 'stable':\n      return `color: #666;`;\n  }\n}}\n`;\n_c13 = TrendIndicator;\nconst AdminReports = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [systemStats, setSystemStats] = useState({\n    totalUsers: 0,\n    totalLeads: 0,\n    completedLeads: 0,\n    pendingLeads: 0,\n    rejectedLeads: 0,\n    averageProcessingTime: 0,\n    systemEfficiency: 0,\n    activeAgents: 0\n  });\n  const [departmentMetrics, setDepartmentMetrics] = useState([]);\n  const [monthlyTrends, setMonthlyTrends] = useState([]);\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      const [dashboardStats, users] = await Promise.all([apiService.getDashboardStats(), apiService.getUsers()]);\n      setSystemStats({\n        totalUsers: users.length || 0,\n        totalLeads: dashboardStats.totalLeads || 0,\n        completedLeads: dashboardStats.completedLeads || 0,\n        pendingLeads: dashboardStats.pendingLeads || 0,\n        rejectedLeads: dashboardStats.rejectedLeads || 0,\n        averageProcessingTime: 2.8,\n        // Mock data\n        systemEfficiency: 87,\n        // Mock data\n        activeAgents: users.filter(u => u.role === 'Agent' && u.isActive).length || 0\n      });\n\n      // Mock department metrics\n      setDepartmentMetrics([{\n        department: 'Personal Loans',\n        totalLeads: 150,\n        completed: 128,\n        pending: 15,\n        rejected: 7,\n        avgTime: 2.5,\n        efficiency: 89\n      }, {\n        department: 'Home Loans',\n        totalLeads: 89,\n        completed: 76,\n        pending: 8,\n        rejected: 5,\n        avgTime: 3.2,\n        efficiency: 85\n      }, {\n        department: 'Car Loans',\n        totalLeads: 67,\n        completed: 58,\n        pending: 6,\n        rejected: 3,\n        avgTime: 2.1,\n        efficiency: 92\n      }]);\n\n      // Mock monthly trends\n      setMonthlyTrends([{\n        month: 'Jan',\n        leads: 120,\n        completed: 105,\n        efficiency: 87\n      }, {\n        month: 'Feb',\n        leads: 135,\n        completed: 118,\n        efficiency: 89\n      }, {\n        month: 'Mar',\n        leads: 156,\n        completed: 142,\n        efficiency: 91\n      }, {\n        month: 'Apr',\n        leads: 142,\n        completed: 128,\n        efficiency: 88\n      }]);\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setSystemStats({\n        totalUsers: 25,\n        totalLeads: 306,\n        completedLeads: 262,\n        pendingLeads: 29,\n        rejectedLeads: 15,\n        averageProcessingTime: 2.8,\n        systemEfficiency: 87,\n        activeAgents: 15\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/admin/dashboard')\n  }, {\n    icon: '👥',\n    label: 'Users',\n    onClick: () => navigate('/admin/users')\n  }, {\n    icon: '📋',\n    label: 'Leads',\n    onClick: () => navigate('/admin/leads')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    active: true\n  }, {\n    icon: '⚙️',\n    label: 'Settings',\n    onClick: () => navigate('/admin/settings')\n  }];\n  const getCompletionRate = () => {\n    return systemStats.totalLeads > 0 ? Math.round(systemStats.completedLeads / systemStats.totalLeads * 100) : 0;\n  };\n  const getSuccessRate = () => {\n    const totalProcessed = systemStats.completedLeads + systemStats.rejectedLeads;\n    return totalProcessed > 0 ? Math.round(systemStats.completedLeads / totalProcessed * 100) : 0;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"System Reports\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"System Reports\",\n    navigationItems: navigationItems,\n    children: /*#__PURE__*/_jsxDEV(ReportsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: timeFilter,\n          onChange: e => setTimeFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"week\",\n            children: \"This Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"month\",\n            children: \"This Month\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"quarter\",\n            children: \"This Quarter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"year\",\n            children: \"This Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => window.print(),\n          children: \"\\uD83D\\uDCC4 Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => navigate('/admin/analytics'),\n          children: \"\\uD83D\\uDCC8 Advanced Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.activeAgents\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Active Agents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.totalLeads\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Leads\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.completedLeads\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.pendingLeads\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Pending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.rejectedLeads\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Rejected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: systemStats.averageProcessingTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Avg. Days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: [systemStats.systemEfficiency, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"System Efficiency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChartContainer, {\n        children: [/*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"Monthly Performance Trends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(4, 1fr)',\n              gap: '15px',\n              textAlign: 'center'\n            },\n            children: monthlyTrends.map((trend, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: 'bold',\n                  marginBottom: '8px'\n                },\n                children: trend.month\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '18px',\n                  color: '#007E3A',\n                  fontWeight: 'bold'\n                },\n                children: trend.completed\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#666'\n                },\n                children: [\"of \", trend.leads]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#2e7d32',\n                  marginTop: '4px'\n                },\n                children: [trend.efficiency, \"% eff.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChartCard, {\n          children: [/*#__PURE__*/_jsxDEV(ChartTitle, {\n            children: \"System Health\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gap: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '32px',\n                  color: '#2e7d32',\n                  fontWeight: 'bold'\n                },\n                children: [getCompletionRate(), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"Completion Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '32px',\n                  color: '#007E3A',\n                  fontWeight: 'bold'\n                },\n                children: [getSuccessRate(), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"Success Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '32px',\n                  color: '#FFD100',\n                  fontWeight: 'bold'\n                },\n                children: [systemStats.systemEfficiency, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666'\n                },\n                children: \"System Efficiency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Department Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(MetricsTable, {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Total Leads\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Rejected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Avg. Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Efficiency\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                  children: \"Trend\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: departmentMetrics.map((dept, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  style: {\n                    fontWeight: '500'\n                  },\n                  children: dept.department\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: dept.totalLeads\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  style: {\n                    color: '#2e7d32'\n                  },\n                  children: dept.completed\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  style: {\n                    color: '#ff8f00'\n                  },\n                  children: dept.pending\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  style: {\n                    color: '#c62828'\n                  },\n                  children: dept.rejected\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [dept.avgTime, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [dept.efficiency, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TrendIndicator, {\n                    trend: dept.efficiency > 88 ? 'up' : dept.efficiency > 85 ? 'stable' : 'down',\n                    children: [dept.efficiency > 88 ? '↗️' : dept.efficiency > 85 ? '➡️' : '↘️', dept.efficiency > 88 ? 'Improving' : dept.efficiency > 85 ? 'Stable' : 'Declining']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#007E3A'\n          },\n          children: \"Executive Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#2e7d32',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83C\\uDFAF Performance Highlights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                paddingLeft: '20px',\n                lineHeight: '1.6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Car Loans department leads with 92% efficiency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Overall system efficiency at 87%, up 3% from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Average processing time reduced to 2.8 days\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#ff8f00',\n                marginBottom: '10px'\n              },\n              children: \"\\u26A0\\uFE0F Areas for Improvement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                paddingLeft: '20px',\n                lineHeight: '1.6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Home Loans processing time needs optimization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Rejection rate in Personal Loans requires attention\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Agent workload distribution could be improved\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#007E3A',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCC8 Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                paddingLeft: '20px',\n                lineHeight: '1.6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Implement best practices from Car Loans team\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Provide additional training for Home Loans agents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Review and update verification guidelines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminReports, \"VWXPeH8TA+02reSRhLzDcwcxx0M=\", false, function () {\n  return [useNavigate];\n});\n_c14 = AdminReports;\nexport default AdminReports;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"ReportsContainer\");\n$RefreshReg$(_c2, \"StatsGrid\");\n$RefreshReg$(_c3, \"StatCard\");\n$RefreshReg$(_c4, \"StatValue\");\n$RefreshReg$(_c5, \"StatLabel\");\n$RefreshReg$(_c6, \"ChartContainer\");\n$RefreshReg$(_c7, \"ChartCard\");\n$RefreshReg$(_c8, \"ChartTitle\");\n$RefreshReg$(_c9, \"FilterContainer\");\n$RefreshReg$(_c0, \"FilterSelect\");\n$RefreshReg$(_c1, \"MetricsTable\");\n$RefreshReg$(_c10, \"TableHeader\");\n$RefreshReg$(_c11, \"TableCell\");\n$RefreshReg$(_c12, \"TableRow\");\n$RefreshReg$(_c13, \"TrendIndicator\");\n$RefreshReg$(_c14, \"AdminReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "ReportsContainer", "div", "_c", "StatsGrid", "_c2", "StatCard", "_c3", "StatValue", "_c4", "StatLabel", "_c5", "ChartContainer", "_c6", "ChartCard", "_c7", "ChartTitle", "h3", "_c8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c9", "FilterSelect", "select", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "_c0", "MetricsTable", "table", "_c1", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c10", "TableCell", "td", "_c11", "TableRow", "tr", "_c12", "TrendIndicator", "span", "trend", "_c13", "AdminReports", "_s", "loading", "setLoading", "timeFilter", "setTimeFilter", "systemStats", "setSystemStats", "totalUsers", "totalLeads", "completedLeads", "pendingLeads", "rejectedLeads", "averageProcessingTime", "systemEfficiency", "activeAgents", "departmentMetrics", "setDepartmentMetrics", "monthlyTrends", "setMonthlyTrends", "navigate", "loadReportsData", "dashboardStats", "users", "Promise", "all", "getDashboardStats", "getUsers", "length", "filter", "u", "role", "isActive", "department", "completed", "pending", "rejected", "avgTime", "efficiency", "month", "leads", "error", "console", "navigationItems", "icon", "label", "onClick", "active", "getCompletionRate", "Math", "round", "getSuccessRate", "totalProcessed", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "variant", "window", "print", "style", "display", "gridTemplateColumns", "gap", "textAlign", "map", "index", "fontSize", "fontWeight", "marginBottom", "color", "marginTop", "overflowX", "dept", "paddingLeft", "lineHeight", "_c14", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Admin/AdminReports.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\n\nconst ReportsContainer = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst StatCard = styled(Card)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #007E3A, #005a2a);\n  color: white;\n`;\n\nconst StatValue = styled.div`\n  font-size: 24px;\n  font-weight: 700;\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 12px;\n  opacity: 0.9;\n  font-weight: 500;\n`;\n\nconst ChartContainer = styled.div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartCard = styled(Card)`\n  padding: 20px;\n`;\n\nconst ChartTitle = styled.h3`\n  margin-bottom: 20px;\n  color: #007E3A;\n  text-align: center;\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst MetricsTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst TrendIndicator = styled.span<{ trend: 'up' | 'down' | 'stable' }>`\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  \n  ${props => {\n    switch (props.trend) {\n      case 'up':\n        return `color: #2e7d32;`;\n      case 'down':\n        return `color: #c62828;`;\n      case 'stable':\n        return `color: #666;`;\n    }\n  }}\n`;\n\nconst AdminReports: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [timeFilter, setTimeFilter] = useState('month');\n  const [systemStats, setSystemStats] = useState({\n    totalUsers: 0,\n    totalLeads: 0,\n    completedLeads: 0,\n    pendingLeads: 0,\n    rejectedLeads: 0,\n    averageProcessingTime: 0,\n    systemEfficiency: 0,\n    activeAgents: 0,\n  });\n  const [departmentMetrics, setDepartmentMetrics] = useState<any[]>([]);\n  const [monthlyTrends, setMonthlyTrends] = useState<any[]>([]);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadReportsData();\n  }, [timeFilter]);\n\n  const loadReportsData = async () => {\n    try {\n      setLoading(true);\n      \n      const [dashboardStats, users] = await Promise.all([\n        apiService.getDashboardStats(),\n        apiService.getUsers(),\n      ]);\n\n      setSystemStats({\n        totalUsers: users.length || 0,\n        totalLeads: dashboardStats.totalLeads || 0,\n        completedLeads: dashboardStats.completedLeads || 0,\n        pendingLeads: dashboardStats.pendingLeads || 0,\n        rejectedLeads: dashboardStats.rejectedLeads || 0,\n        averageProcessingTime: 2.8, // Mock data\n        systemEfficiency: 87, // Mock data\n        activeAgents: users.filter(u => u.role === 'Agent' && u.isActive).length || 0,\n      });\n\n      // Mock department metrics\n      setDepartmentMetrics([\n        {\n          department: 'Personal Loans',\n          totalLeads: 150,\n          completed: 128,\n          pending: 15,\n          rejected: 7,\n          avgTime: 2.5,\n          efficiency: 89,\n        },\n        {\n          department: 'Home Loans',\n          totalLeads: 89,\n          completed: 76,\n          pending: 8,\n          rejected: 5,\n          avgTime: 3.2,\n          efficiency: 85,\n        },\n        {\n          department: 'Car Loans',\n          totalLeads: 67,\n          completed: 58,\n          pending: 6,\n          rejected: 3,\n          avgTime: 2.1,\n          efficiency: 92,\n        },\n      ]);\n\n      // Mock monthly trends\n      setMonthlyTrends([\n        { month: 'Jan', leads: 120, completed: 105, efficiency: 87 },\n        { month: 'Feb', leads: 135, completed: 118, efficiency: 89 },\n        { month: 'Mar', leads: 156, completed: 142, efficiency: 91 },\n        { month: 'Apr', leads: 142, completed: 128, efficiency: 88 },\n      ]);\n\n    } catch (error) {\n      console.error('Error loading reports data:', error);\n      // Use mock data on error\n      setSystemStats({\n        totalUsers: 25,\n        totalLeads: 306,\n        completedLeads: 262,\n        pendingLeads: 29,\n        rejectedLeads: 15,\n        averageProcessingTime: 2.8,\n        systemEfficiency: 87,\n        activeAgents: 15,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/admin/dashboard') },\n    { icon: '👥', label: 'Users', onClick: () => navigate('/admin/users') },\n    { icon: '📋', label: 'Leads', onClick: () => navigate('/admin/leads') },\n    { icon: '📊', label: 'Reports', active: true },\n    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/admin/settings') },\n  ];\n\n  const getCompletionRate = () => {\n    return systemStats.totalLeads > 0 ? Math.round((systemStats.completedLeads / systemStats.totalLeads) * 100) : 0;\n  };\n\n  const getSuccessRate = () => {\n    const totalProcessed = systemStats.completedLeads + systemStats.rejectedLeads;\n    return totalProcessed > 0 ? Math.round((systemStats.completedLeads / totalProcessed) * 100) : 0;\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"System Reports\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"System Reports\" navigationItems={navigationItems}>\n      <ReportsContainer>\n        <FilterContainer>\n          <FilterSelect value={timeFilter} onChange={(e) => setTimeFilter(e.target.value)}>\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </FilterSelect>\n          \n          <Button variant=\"outline\" onClick={() => window.print()}>\n            📄 Export Report\n          </Button>\n          \n          <Button variant=\"secondary\" onClick={() => navigate('/admin/analytics')}>\n            📈 Advanced Analytics\n          </Button>\n        </FilterContainer>\n\n        {/* System Overview Stats */}\n        <StatsGrid>\n          <StatCard>\n            <StatValue>{systemStats.totalUsers}</StatValue>\n            <StatLabel>Total Users</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.activeAgents}</StatValue>\n            <StatLabel>Active Agents</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.totalLeads}</StatValue>\n            <StatLabel>Total Leads</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.completedLeads}</StatValue>\n            <StatLabel>Completed</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.pendingLeads}</StatValue>\n            <StatLabel>Pending</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.rejectedLeads}</StatValue>\n            <StatLabel>Rejected</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.averageProcessingTime}</StatValue>\n            <StatLabel>Avg. Days</StatLabel>\n          </StatCard>\n          <StatCard>\n            <StatValue>{systemStats.systemEfficiency}%</StatValue>\n            <StatLabel>System Efficiency</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        {/* Charts and Trends */}\n        <ChartContainer>\n          <ChartCard>\n            <ChartTitle>Monthly Performance Trends</ChartTitle>\n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '15px', textAlign: 'center' }}>\n              {monthlyTrends.map((trend, index) => (\n                <div key={index}>\n                  <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '8px' }}>\n                    {trend.month}\n                  </div>\n                  <div style={{ fontSize: '18px', color: '#007E3A', fontWeight: 'bold' }}>\n                    {trend.completed}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#666' }}>\n                    of {trend.leads}\n                  </div>\n                  <div style={{ fontSize: '12px', color: '#2e7d32', marginTop: '4px' }}>\n                    {trend.efficiency}% eff.\n                  </div>\n                </div>\n              ))}\n            </div>\n          </ChartCard>\n\n          <ChartCard>\n            <ChartTitle>System Health</ChartTitle>\n            <div style={{ display: 'grid', gap: '15px' }}>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#2e7d32', fontWeight: 'bold' }}>\n                  {getCompletionRate()}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Completion Rate</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#007E3A', fontWeight: 'bold' }}>\n                  {getSuccessRate()}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>Success Rate</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', color: '#FFD100', fontWeight: 'bold' }}>\n                  {systemStats.systemEfficiency}%\n                </div>\n                <div style={{ fontSize: '14px', color: '#666' }}>System Efficiency</div>\n              </div>\n            </div>\n          </ChartCard>\n        </ChartContainer>\n\n        {/* Department Performance */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Department Performance</h3>\n          <div style={{ overflowX: 'auto' }}>\n            <MetricsTable>\n              <thead>\n                <tr>\n                  <TableHeader>Department</TableHeader>\n                  <TableHeader>Total Leads</TableHeader>\n                  <TableHeader>Completed</TableHeader>\n                  <TableHeader>Pending</TableHeader>\n                  <TableHeader>Rejected</TableHeader>\n                  <TableHeader>Avg. Time</TableHeader>\n                  <TableHeader>Efficiency</TableHeader>\n                  <TableHeader>Trend</TableHeader>\n                </tr>\n              </thead>\n              <tbody>\n                {departmentMetrics.map((dept, index) => (\n                  <TableRow key={index}>\n                    <TableCell style={{ fontWeight: '500' }}>{dept.department}</TableCell>\n                    <TableCell>{dept.totalLeads}</TableCell>\n                    <TableCell style={{ color: '#2e7d32' }}>{dept.completed}</TableCell>\n                    <TableCell style={{ color: '#ff8f00' }}>{dept.pending}</TableCell>\n                    <TableCell style={{ color: '#c62828' }}>{dept.rejected}</TableCell>\n                    <TableCell>{dept.avgTime} days</TableCell>\n                    <TableCell>{dept.efficiency}%</TableCell>\n                    <TableCell>\n                      <TrendIndicator trend={dept.efficiency > 88 ? 'up' : dept.efficiency > 85 ? 'stable' : 'down'}>\n                        {dept.efficiency > 88 ? '↗️' : dept.efficiency > 85 ? '➡️' : '↘️'}\n                        {dept.efficiency > 88 ? 'Improving' : dept.efficiency > 85 ? 'Stable' : 'Declining'}\n                      </TrendIndicator>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </tbody>\n            </MetricsTable>\n          </div>\n        </Card>\n\n        {/* Key Insights */}\n        <Card>\n          <h3 style={{ marginBottom: '20px', color: '#007E3A' }}>Executive Summary</h3>\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>\n            <div>\n              <h4 style={{ color: '#2e7d32', marginBottom: '10px' }}>🎯 Performance Highlights</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Car Loans department leads with 92% efficiency</li>\n                <li>Overall system efficiency at 87%, up 3% from last month</li>\n                <li>Average processing time reduced to 2.8 days</li>\n              </ul>\n            </div>\n            <div>\n              <h4 style={{ color: '#ff8f00', marginBottom: '10px' }}>⚠️ Areas for Improvement</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Home Loans processing time needs optimization</li>\n                <li>Rejection rate in Personal Loans requires attention</li>\n                <li>Agent workload distribution could be improved</li>\n              </ul>\n            </div>\n            <div>\n              <h4 style={{ color: '#007E3A', marginBottom: '10px' }}>📈 Recommendations</h4>\n              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>\n                <li>Implement best practices from Car Loans team</li>\n                <li>Provide additional training for Home Loans agents</li>\n                <li>Review and update verification guidelines</li>\n              </ul>\n            </div>\n          </div>\n        </Card>\n      </ReportsContainer>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,gBAAgB,GAAGR,MAAM,CAACS,GAAG;AACnC;AACA;AACA,CAAC;AAACC,EAAA,GAHIF,gBAAgB;AAKtB,MAAMG,SAAS,GAAGX,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAGb,MAAM,CAACE,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GARID,QAAQ;AAUd,MAAME,SAAS,GAAGf,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGjB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAJID,SAAS;AAMf,MAAME,cAAc,GAAGnB,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GATID,cAAc;AAWpB,MAAME,SAAS,GAAGrB,MAAM,CAACE,IAAI,CAAC;AAC9B;AACA,CAAC;AAACoB,GAAA,GAFID,SAAS;AAIf,MAAME,UAAU,GAAGvB,MAAM,CAACwB,EAAE;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,eAAe,GAAG1B,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAG5B,MAAM,CAAC6B,MAAM;AAClC;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACC,GAAA,GAXIT,YAAY;AAalB,MAAMU,YAAY,GAAGtC,MAAM,CAACuC,KAAK;AACjC;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGzC,MAAM,CAAC0C,EAAE;AAC7B;AACA;AACA,6BAA6BZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAClE,sBAAsBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,QAAQ;AAC1D;AACA,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,UAAU;AACjD,CAAC;AAACC,IAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAG/C,MAAM,CAACgD,EAAE;AAC3B;AACA;AACA,6BAA6BlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAClE,CAAC;AAACM,IAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGlD,MAAM,CAACmD,EAAE;AAC1B;AACA,wBAAwBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,SAAS;AAC7D;AACA,CAAC;AAACS,IAAA,GAJIF,QAAQ;AAMd,MAAMG,cAAc,GAAGrD,MAAM,CAACsD,IAAyC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,IAAIxB,KAAK,IAAI;EACT,QAAQA,KAAK,CAACyB,KAAK;IACjB,KAAK,IAAI;MACP,OAAO,iBAAiB;IAC1B,KAAK,MAAM;MACT,OAAO,iBAAiB;IAC1B,KAAK,QAAQ;MACX,OAAO,cAAc;EACzB;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GAjBIH,cAAc;AAmBpB,MAAMI,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,OAAO,CAAC;EACrD,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC;IAC7CoE,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,qBAAqB,EAAE,CAAC;IACxBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7E,QAAQ,CAAQ,EAAE,CAAC;EACrE,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAMgF,QAAQ,GAAG9E,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACdgF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACjB,UAAU,CAAC,CAAC;EAEhB,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM,CAACmB,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD7E,UAAU,CAAC8E,iBAAiB,CAAC,CAAC,EAC9B9E,UAAU,CAAC+E,QAAQ,CAAC,CAAC,CACtB,CAAC;MAEFpB,cAAc,CAAC;QACbC,UAAU,EAAEe,KAAK,CAACK,MAAM,IAAI,CAAC;QAC7BnB,UAAU,EAAEa,cAAc,CAACb,UAAU,IAAI,CAAC;QAC1CC,cAAc,EAAEY,cAAc,CAACZ,cAAc,IAAI,CAAC;QAClDC,YAAY,EAAEW,cAAc,CAACX,YAAY,IAAI,CAAC;QAC9CC,aAAa,EAAEU,cAAc,CAACV,aAAa,IAAI,CAAC;QAChDC,qBAAqB,EAAE,GAAG;QAAE;QAC5BC,gBAAgB,EAAE,EAAE;QAAE;QACtBC,YAAY,EAAEQ,KAAK,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,IAAID,CAAC,CAACE,QAAQ,CAAC,CAACJ,MAAM,IAAI;MAC9E,CAAC,CAAC;;MAEF;MACAX,oBAAoB,CAAC,CACnB;QACEgB,UAAU,EAAE,gBAAgB;QAC5BxB,UAAU,EAAE,GAAG;QACfyB,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,EAAE;QACXC,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,UAAU,EAAE,YAAY;QACxBxB,UAAU,EAAE,EAAE;QACdyB,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,UAAU,EAAE,WAAW;QACvBxB,UAAU,EAAE,EAAE;QACdyB,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;MACd,CAAC,CACF,CAAC;;MAEF;MACAnB,gBAAgB,CAAC,CACf;QAAEoB,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEN,SAAS,EAAE,GAAG;QAAEI,UAAU,EAAE;MAAG,CAAC,EAC5D;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEN,SAAS,EAAE,GAAG;QAAEI,UAAU,EAAE;MAAG,CAAC,EAC5D;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEN,SAAS,EAAE,GAAG;QAAEI,UAAU,EAAE;MAAG,CAAC,EAC5D;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,GAAG;QAAEN,SAAS,EAAE,GAAG;QAAEI,UAAU,EAAE;MAAG,CAAC,CAC7D,CAAC;IAEJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACAlC,cAAc,CAAC;QACbC,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,GAAG;QACfC,cAAc,EAAE,GAAG;QACnBC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,qBAAqB,EAAE,GAAG;QAC1BC,gBAAgB,EAAE,EAAE;QACpBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC/E;IAAEwB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,OAAO;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,cAAc;EAAE,CAAC,EACvE;IAAEwB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,OAAO;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,cAAc;EAAE,CAAC,EACvE;IAAEwB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEE,MAAM,EAAE;EAAK,CAAC,EAC9C;IAAEH,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,iBAAiB;EAAE,CAAC,CAC9E;EAED,MAAM4B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAO1C,WAAW,CAACG,UAAU,GAAG,CAAC,GAAGwC,IAAI,CAACC,KAAK,CAAE5C,WAAW,CAACI,cAAc,GAAGJ,WAAW,CAACG,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;EACjH,CAAC;EAED,MAAM0C,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,cAAc,GAAG9C,WAAW,CAACI,cAAc,GAAGJ,WAAW,CAACM,aAAa;IAC7E,OAAOwC,cAAc,GAAG,CAAC,GAAGH,IAAI,CAACC,KAAK,CAAE5C,WAAW,CAACI,cAAc,GAAG0C,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;EACjG,CAAC;EAED,IAAIlD,OAAO,EAAE;IACX,oBACEpD,OAAA,CAACN,eAAe;MAAC6G,KAAK,EAAC,gBAAgB;MAACV,eAAe,EAAEA,eAAgB;MAAAW,QAAA,eACvExG,OAAA,CAACH,cAAc;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACE5G,OAAA,CAACN,eAAe;IAAC6G,KAAK,EAAC,gBAAgB;IAACV,eAAe,EAAEA,eAAgB;IAAAW,QAAA,eACvExG,OAAA,CAACC,gBAAgB;MAAAuG,QAAA,gBACfxG,OAAA,CAACmB,eAAe;QAAAqF,QAAA,gBACdxG,OAAA,CAACqB,YAAY;UAACwF,KAAK,EAAEvD,UAAW;UAACwD,QAAQ,EAAGC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAL,QAAA,gBAC9ExG,OAAA;YAAQ6G,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC5G,OAAA;YAAQ6G,KAAK,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzC5G,OAAA;YAAQ6G,KAAK,EAAC,SAAS;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7C5G,OAAA;YAAQ6G,KAAK,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEf5G,OAAA,CAACJ,MAAM;UAACqH,OAAO,EAAC,SAAS;UAACjB,OAAO,EAAEA,CAAA,KAAMkB,MAAM,CAACC,KAAK,CAAC,CAAE;UAAAX,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5G,OAAA,CAACJ,MAAM;UAACqH,OAAO,EAAC,WAAW;UAACjB,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB,CAAE;UAAAkC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGlB5G,OAAA,CAACI,SAAS;QAAAoG,QAAA,gBACRxG,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACE;UAAU;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACS;UAAY;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjD5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACG;UAAU;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACI;UAAc;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnD5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACK;UAAY;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjD5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACM;UAAa;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClD5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,EAAEhD,WAAW,CAACO;UAAqB;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1D5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACX5G,OAAA,CAACM,QAAQ;UAAAkG,QAAA,gBACPxG,OAAA,CAACQ,SAAS;YAAAgG,QAAA,GAAEhD,WAAW,CAACQ,gBAAgB,EAAC,GAAC;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACtD5G,OAAA,CAACU,SAAS;YAAA8F,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGZ5G,OAAA,CAACY,cAAc;QAAA4F,QAAA,gBACbxG,OAAA,CAACc,SAAS;UAAA0F,QAAA,gBACRxG,OAAA,CAACgB,UAAU;YAAAwF,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnD5G,OAAA;YAAKoH,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,mBAAmB,EAAE,gBAAgB;cAAEC,GAAG,EAAE,MAAM;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAhB,QAAA,EACtGpC,aAAa,CAACqD,GAAG,CAAC,CAACzE,KAAK,EAAE0E,KAAK,kBAC9B1H,OAAA;cAAAwG,QAAA,gBACExG,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAArB,QAAA,EACvExD,KAAK,CAACyC;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,EACpExD,KAAK,CAACoC;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,GAAC,KAC5C,EAACxD,KAAK,CAAC0C,KAAK;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE,SAAS;kBAAEC,SAAS,EAAE;gBAAM,CAAE;gBAAAvB,QAAA,GAClExD,KAAK,CAACwC,UAAU,EAAC,QACpB;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAZEc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEZ5G,OAAA,CAACc,SAAS;UAAA0F,QAAA,gBACRxG,OAAA,CAACgB,UAAU;YAAAwF,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtC5G,OAAA;YAAKoH,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE;YAAO,CAAE;YAAAf,QAAA,gBAC3CxG,OAAA;cAAKoH,KAAK,EAAE;gBAAEI,SAAS,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBAClCxG,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,GACpEN,iBAAiB,CAAC,CAAC,EAAC,GACvB;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN5G,OAAA;cAAKoH,KAAK,EAAE;gBAAEI,SAAS,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBAClCxG,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,GACpEH,cAAc,CAAC,CAAC,EAAC,GACpB;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN5G,OAAA;cAAKoH,KAAK,EAAE;gBAAEI,SAAS,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBAClCxG,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,GACpEhD,WAAW,CAACQ,gBAAgB,EAAC,GAChC;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAKoH,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEG,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGjB5G,OAAA,CAACL,IAAI;QAAA6G,QAAA,gBACHxG,OAAA;UAAIoH,KAAK,EAAE;YAAES,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAtB,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClF5G,OAAA;UAAKoH,KAAK,EAAE;YAAEY,SAAS,EAAE;UAAO,CAAE;UAAAxB,QAAA,eAChCxG,OAAA,CAAC+B,YAAY;YAAAyE,QAAA,gBACXxG,OAAA;cAAAwG,QAAA,eACExG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACtC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAClC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrC5G,OAAA,CAACkC,WAAW;kBAAAsE,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR5G,OAAA;cAAAwG,QAAA,EACGtC,iBAAiB,CAACuD,GAAG,CAAC,CAACQ,IAAI,EAAEP,KAAK,kBACjC1H,OAAA,CAAC2C,QAAQ;gBAAA6D,QAAA,gBACPxG,OAAA,CAACwC,SAAS;kBAAC4E,KAAK,EAAE;oBAAEQ,UAAU,EAAE;kBAAM,CAAE;kBAAApB,QAAA,EAAEyB,IAAI,CAAC9C;gBAAU;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE5G,OAAA,CAACwC,SAAS;kBAAAgE,QAAA,EAAEyB,IAAI,CAACtE;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC5G,OAAA,CAACwC,SAAS;kBAAC4E,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU,CAAE;kBAAAtB,QAAA,EAAEyB,IAAI,CAAC7C;gBAAS;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpE5G,OAAA,CAACwC,SAAS;kBAAC4E,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU,CAAE;kBAAAtB,QAAA,EAAEyB,IAAI,CAAC5C;gBAAO;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClE5G,OAAA,CAACwC,SAAS;kBAAC4E,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU,CAAE;kBAAAtB,QAAA,EAAEyB,IAAI,CAAC3C;gBAAQ;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnE5G,OAAA,CAACwC,SAAS;kBAAAgE,QAAA,GAAEyB,IAAI,CAAC1C,OAAO,EAAC,OAAK;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC1C5G,OAAA,CAACwC,SAAS;kBAAAgE,QAAA,GAAEyB,IAAI,CAACzC,UAAU,EAAC,GAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACzC5G,OAAA,CAACwC,SAAS;kBAAAgE,QAAA,eACRxG,OAAA,CAAC8C,cAAc;oBAACE,KAAK,EAAEiF,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,IAAI,GAAGyC,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,QAAQ,GAAG,MAAO;oBAAAgB,QAAA,GAC3FyB,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,IAAI,GAAGyC,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAChEyC,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,WAAW,GAAGyC,IAAI,CAACzC,UAAU,GAAG,EAAE,GAAG,QAAQ,GAAG,WAAW;kBAAA;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,GAbCc,KAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGP5G,OAAA,CAACL,IAAI;QAAA6G,QAAA,gBACHxG,OAAA;UAAIoH,KAAK,EAAE;YAAES,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAtB,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E5G,OAAA;UAAKoH,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAf,QAAA,gBACxGxG,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAIoH,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAED,YAAY,EAAE;cAAO,CAAE;cAAArB,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrF5G,OAAA;cAAIoH,KAAK,EAAE;gBAAEc,WAAW,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAA3B,QAAA,gBACpDxG,OAAA;gBAAAwG,QAAA,EAAI;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvD5G,OAAA;gBAAAwG,QAAA,EAAI;cAAuD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChE5G,OAAA;gBAAAwG,QAAA,EAAI;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN5G,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAIoH,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAED,YAAY,EAAE;cAAO,CAAE;cAAArB,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF5G,OAAA;cAAIoH,KAAK,EAAE;gBAAEc,WAAW,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAA3B,QAAA,gBACpDxG,OAAA;gBAAAwG,QAAA,EAAI;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtD5G,OAAA;gBAAAwG,QAAA,EAAI;cAAmD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D5G,OAAA;gBAAAwG,QAAA,EAAI;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN5G,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAIoH,KAAK,EAAE;gBAAEU,KAAK,EAAE,SAAS;gBAAED,YAAY,EAAE;cAAO,CAAE;cAAArB,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E5G,OAAA;cAAIoH,KAAK,EAAE;gBAAEc,WAAW,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAM,CAAE;cAAA3B,QAAA,gBACpDxG,OAAA;gBAAAwG,QAAA,EAAI;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD5G,OAAA;gBAAAwG,QAAA,EAAI;cAAiD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D5G,OAAA;gBAAAwG,QAAA,EAAI;cAAyC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEtB,CAAC;AAACzD,EAAA,CA7SID,YAAsB;EAAA,QAeT1D,WAAW;AAAA;AAAA4I,IAAA,GAfxBlF,YAAsB;AA+S5B,eAAeA,YAAY;AAAC,IAAA/C,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAM,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAmF,IAAA;AAAAC,YAAA,CAAAlI,EAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}