2025-05-29 18:38:55.728 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:55.856 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:38:55.877 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:56.040 +05:30 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-05-29 18:38:56.052 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 18:38:56.070 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 18:38:56.121 +05:30 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 18:38:56.153 +05:30 [INF] Applying migration '20250529130711_InitialCreate'.
2025-05-29 18:38:56.291 +05:30 [ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
);
2025-05-29 18:38:56.308 +05:30 [ERR] An error occurred while creating the database or seeding data
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:78b9cce1-bfd3-43c0-9e96-c8d4ed1db502
Error Number:2714,State:6,Class:16
2025-05-29 18:38:56.340 +05:30 [INF] UBI-CPV API starting up...
2025-05-29 18:38:56.375 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 18:38:56.640 +05:30 [INF] Now listening on: https://localhost:59358
2025-05-29 18:38:56.649 +05:30 [INF] Now listening on: http://localhost:59359
2025-05-29 18:38:56.660 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-29 18:38:56.665 +05:30 [INF] Hosting environment: Development
2025-05-29 18:38:56.671 +05:30 [INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API
2025-05-29 18:39:21.220 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger - null null
2025-05-29 18:39:21.325 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger - 301 0 null 106.1584ms
2025-05-29 18:39:21.353 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 18:39:21.421 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 68.0438ms
2025-05-29 18:39:21.499 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null
2025-05-29 18:39:21.559 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null
2025-05-29 18:39:21.559 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null
2025-05-29 18:39:21.567 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null
2025-05-29 18:39:21.572 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-29 18:39:21.624 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 124.8693ms
2025-05-29 18:39:21.629 +05:30 [INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css'
2025-05-29 18:39:21.631 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-29 18:39:21.639 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-29 18:39:21.674 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 114.9409ms
2025-05-29 18:39:21.676 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 108.9907ms
2025-05-29 18:39:21.678 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 118.7596ms
2025-05-29 18:39:21.914 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 18:39:22.094 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 179.9137ms
2025-05-29 18:39:51.748 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null
2025-05-29 18:39:51.765 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 17.3231ms
2025-05-29 18:39:51.945 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null
2025-05-29 18:39:51.952 +05:30 [INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null
2025-05-29 18:39:52.009 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-29 18:39:52.031 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 79.2948ms
2025-05-29 18:39:52.109 +05:30 [INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 163.3942ms
[2025-05-29 18:45:00.305 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.478 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.510 +05:30 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1 {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.534 +05:30 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]'); {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.578 +05:30 INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId]; {"EventId":{"Id":20101,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandExecuted"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:00.647 +05:30 INF] Applying migration '20250529130711_InitialCreate'. {"EventId":{"Id":20402,"Name":"Microsoft.EntityFrameworkCore.Migrations.MigrationApplying"},"SourceContext":"Microsoft.EntityFrameworkCore.Migrations"}
[2025-05-29 18:45:00.841 +05:30 ERR] Failed executing DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [DocumentTypes] (
    [DocumentTypeId] int NOT NULL IDENTITY,
    [TypeName] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_DocumentTypes] PRIMARY KEY ([DocumentTypeId])
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-05-29 18:45:01.010 +05:30 ERR] An error occurred while creating the database or seeding data {}
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'DocumentTypes' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 205
ClientConnectionId:80448c5e-53d1-4b2e-90e7-f1a13782a954
Error Number:2714,State:6,Class:16
[2025-05-29 18:45:01.117 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-29 18:45:01.223 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-29 18:45:01.800 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.822 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.922 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.928 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:01.933 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:45:03.070 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/ - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:00000001","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:03.739 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/ - 404 0 null 681.9606ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:00000001","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
[2025-05-29 18:45:03.818 +05:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59358/, Response status code: 404 {"EventId":{"Id":16},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCUMB8T0DL8:00000001","RequestPath":"/","ConnectionId":"0HNCUMB8T0DL8"}
