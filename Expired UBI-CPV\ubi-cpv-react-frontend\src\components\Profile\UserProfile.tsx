import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import DashboardLayout from '../Layout/DashboardLayout';
import { <PERSON>, But<PERSON>, LoadingSpinner } from '../../styles/GlobalStyles';
import { apiService, User } from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';

const ProfileContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ProfileCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 30px;
`;

const Avatar = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007E3A, #005a2a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  font-weight: bold;
  margin-bottom: 20px;
`;

const UserName = styled.h2`
  margin-bottom: 8px;
  color: ${props => props.theme.colors.textDark};
`;

const UserRole = styled.div`
  color: ${props => props.theme.colors.textMedium};
  font-size: 16px;
  margin-bottom: 20px;
`;

const StatusBadge = styled.span<{ isActive: boolean }>`
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  background-color: ${props => props.isActive ? '#e8f5e9' : '#ffebee'};
  color: ${props => props.isActive ? '#2e7d32' : '#c62828'};
`;

const FormContainer = styled.div`
  display: grid;
  gap: 20px;
`;

const FormSection = styled(Card)`
  padding: 20px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 20px;
  color: #007E3A;
  border-bottom: 1px solid ${props => props.theme.colors.lightGray};
  padding-bottom: 10px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: ${props => props.theme.colors.textMedium};
`;

const Input = styled.input`
  width: 100%;
  padding: 10px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
  }
  
  &:disabled {
    background-color: ${props => props.theme.colors.lightGray};
    cursor: not-allowed;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: 10px 12px;
  border: 1px solid ${props => props.theme.colors.mediumGray};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 126, 58, 0.1);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 20px;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 15px;
  background: ${props => props.theme.colors.offWhite};
  border-radius: ${props => props.theme.borderRadius.sm};
`;

const StatValue = styled.div`
  font-size: 20px;
  font-weight: bold;
  color: #007E3A;
  margin-bottom: 5px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textLight};
`;

const UserProfile: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    bio: '',
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [userStats, setUserStats] = useState({
    totalTasks: 0,
    completedTasks: 0,
    approvalRate: 0,
    avgTime: 0,
  });
  const navigate = useNavigate();

  useEffect(() => {
    loadUserProfile();
    loadUserStats();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const userData = await apiService.getCurrentUser();
      setUser(userData);
      setFormData({
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phoneNumber: userData.phoneNumber || '',
        bio: '', // This would come from user profile if available
      });
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Use current user from context as fallback
      if (currentUser) {
        setUser(currentUser);
        setFormData({
          firstName: currentUser.firstName || '',
          lastName: currentUser.lastName || '',
          email: currentUser.email || '',
          phoneNumber: currentUser.phoneNumber || '',
          bio: '',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    try {
      if (currentUser?.role === 'Agent') {
        const stats = await apiService.getAgentDashboardStats();
        setUserStats({
          totalTasks: stats.totalAssigned || 0,
          completedTasks: stats.completedLeads || 0,
          approvalRate: 85, // Mock data
          avgTime: 2.5, // Mock data
        });
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const navigationItems = [
    { 
      icon: '🏠', 
      label: 'Dashboard', 
      onClick: () => {
        switch (currentUser?.role) {
          case 'Agent':
            navigate('/agent/dashboard');
            break;
          case 'Supervisor':
            navigate('/supervisor/dashboard');
            break;
          case 'Admin':
            navigate('/admin/dashboard');
            break;
          default:
            navigate('/');
        }
      }
    },
    { icon: '👤', label: 'Profile', active: true },
    { icon: '⚙️', label: 'Settings', onClick: () => navigate('/settings') },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSaveProfile = async () => {
    try {
      if (!user) return;
      
      await apiService.updateUser(user.userId, formData);
      
      // Update local user state
      setUser(prev => prev ? { ...prev, ...formData } : null);
      setEditing(false);
      
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    }
  };

  const handleChangePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }
    
    if (passwordData.newPassword.length < 6) {
      alert('Password must be at least 6 characters long');
      return;
    }

    try {
      // This would be a dedicated change password endpoint
      alert('Password change functionality would be implemented here');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      console.error('Error changing password:', error);
      alert('Failed to change password');
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <DashboardLayout title="User Profile" navigationItems={navigationItems}>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  if (!user) {
    return (
      <DashboardLayout title="User Profile" navigationItems={navigationItems}>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            User profile not found
          </div>
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="User Profile" navigationItems={navigationItems}>
      <ProfileContainer>
        {/* Profile Summary */}
        <ProfileCard>
          <Avatar>
            {getInitials(user.firstName, user.lastName)}
          </Avatar>
          <UserName>{user.firstName} {user.lastName}</UserName>
          <UserRole>{user.role}</UserRole>
          <StatusBadge isActive={user.isActive}>
            {user.isActive ? 'Active' : 'Inactive'}
          </StatusBadge>
          
          <div style={{ marginTop: '20px', width: '100%' }}>
            <div style={{ marginBottom: '10px' }}>
              <strong>Username:</strong> {user.username}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Email:</strong> {user.email}
            </div>
            <div style={{ marginBottom: '10px' }}>
              <strong>Joined:</strong> {formatDate(user.createdDate)}
            </div>
            {user.lastLoginDate && (
              <div>
                <strong>Last Login:</strong> {formatDate(user.lastLoginDate)}
              </div>
            )}
          </div>

          {/* User Stats for Agents */}
          {user.role === 'Agent' && (
            <StatsGrid>
              <StatItem>
                <StatValue>{userStats.totalTasks}</StatValue>
                <StatLabel>Total Tasks</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{userStats.completedTasks}</StatValue>
                <StatLabel>Completed</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{userStats.approvalRate}%</StatValue>
                <StatLabel>Approval Rate</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{userStats.avgTime}</StatValue>
                <StatLabel>Avg. Days</StatLabel>
              </StatItem>
            </StatsGrid>
          )}
        </ProfileCard>

        {/* Profile Form */}
        <FormContainer>
          {/* Personal Information */}
          <FormSection>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <SectionTitle style={{ marginBottom: 0 }}>Personal Information</SectionTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditing(!editing)}
              >
                {editing ? 'Cancel' : 'Edit'}
              </Button>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
              <FormGroup>
                <Label>First Name</Label>
                <Input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  disabled={!editing}
                />
              </FormGroup>
              <FormGroup>
                <Label>Last Name</Label>
                <Input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  disabled={!editing}
                />
              </FormGroup>
            </div>

            <FormGroup>
              <Label>Email</Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                disabled={!editing}
              />
            </FormGroup>

            <FormGroup>
              <Label>Phone Number</Label>
              <Input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                disabled={!editing}
              />
            </FormGroup>

            <FormGroup>
              <Label>Bio</Label>
              <TextArea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                disabled={!editing}
                placeholder="Tell us about yourself..."
              />
            </FormGroup>

            {editing && (
              <ButtonGroup>
                <Button variant="outline" onClick={() => setEditing(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveProfile}>
                  Save Changes
                </Button>
              </ButtonGroup>
            )}
          </FormSection>

          {/* Change Password */}
          <FormSection>
            <SectionTitle>Change Password</SectionTitle>

            <FormGroup>
              <Label>Current Password</Label>
              <Input
                type="password"
                name="currentPassword"
                value={passwordData.currentPassword}
                onChange={handlePasswordChange}
              />
            </FormGroup>

            <FormGroup>
              <Label>New Password</Label>
              <Input
                type="password"
                name="newPassword"
                value={passwordData.newPassword}
                onChange={handlePasswordChange}
              />
            </FormGroup>

            <FormGroup>
              <Label>Confirm New Password</Label>
              <Input
                type="password"
                name="confirmPassword"
                value={passwordData.confirmPassword}
                onChange={handlePasswordChange}
              />
            </FormGroup>

            <ButtonGroup>
              <Button
                variant="secondary"
                onClick={handleChangePassword}
                disabled={!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
              >
                Change Password
              </Button>
            </ButtonGroup>
          </FormSection>
        </FormContainer>
      </ProfileContainer>
    </DashboardLayout>
  );
};

export default UserProfile;
