[2025-05-29 18:04:59.335 +05:30 ERR] An error occurred while creating the database or seeding data {}
System.ArgumentException: Connection string keyword 'initial catalog' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.GetIndex(String keyword)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.set_Item(String keyword, Object value)
   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)
   at Microsoft.Data.Sqlite.SqliteConnectionStringBuilder..ctor(String connectionString)
   at Microsoft.EntityFrameworkCore.Sqlite.Storage.Internal.SqliteDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\Program.cs:line 203
[2025-05-29 18:04:59.532 +05:30 INF] UBI-CPV API starting up... {}
[2025-05-29 18:04:59.829 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest. {"EventId":{"Id":63,"Name":"UsingProfileAsKeyRepositoryWithDPAPI"},"SourceContext":"Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager"}
[2025-05-29 18:05:00.522 +05:30 INF] Now listening on: https://localhost:59358 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:05:00.548 +05:30 INF] Now listening on: http://localhost:59359 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:05:00.563 +05:30 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:05:00.578 +05:30 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:05:00.584 +05:30 INF] Content root path: D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-05-29 18:05:21.397 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000001","RequestPath":"/swagger","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:21.773 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger - 301 0 null 370.9317ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000001","RequestPath":"/swagger","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:21.818 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/index.html - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000002","RequestPath":"/swagger/index.html","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:21.918 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/index.html - 200 null text/html;charset=utf-8 99.2274ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000002","RequestPath":"/swagger/index.html","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:21.973 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:22.015 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULL3CQ9FF:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:22.026 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FK:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULL3CQ9FK"}
[2025-05-29 18:05:22.026 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FJ:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.027 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FL:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULL3CQ9FL"}
[2025-05-29 18:05:22.062 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui.css - 200 144929 text/css 88.5761ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FF:00000003","RequestPath":"/swagger/swagger-ui.css","ConnectionId":"0HNCULL3CQ9FF"}
[2025-05-29 18:05:22.085 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULL3CQ9FK:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULL3CQ9FK"}
[2025-05-29 18:05:22.095 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULL3CQ9FL:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULL3CQ9FL"}
[2025-05-29 18:05:22.131 +05:30 INF] Sending file. Request path: '/swagger-ui/custom.css'. Physical path: 'D:\Augment-projects\Expired UBI-CPV\Expired UBI-CPV\UBI.CPV.API\wwwroot\swagger-ui\custom.css' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULL3CQ9FJ:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.132 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-standalone-preset.js - 200 312163 text/javascript 106.2456ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FK:00000001","RequestPath":"/swagger/swagger-ui-standalone-preset.js","ConnectionId":"0HNCULL3CQ9FK"}
[2025-05-29 18:05:22.133 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/swagger-ui-bundle.js - 200 1061536 text/javascript 105.56ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FL:00000001","RequestPath":"/swagger/swagger-ui-bundle.js","ConnectionId":"0HNCULL3CQ9FL"}
[2025-05-29 18:05:22.167 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger-ui/custom.css - 200 2223 text/css 140.9531ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FJ:00000001","RequestPath":"/swagger-ui/custom.css","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.565 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FL:00000002","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCULL3CQ9FL"}
[2025-05-29 18:05:22.590 +05:30 INF] Request starting HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - null null {"EventId":{"Id":1},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FJ:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.628 +05:30 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A' {"EventId":{"Id":2,"Name":"FileServed"},"SourceContext":"Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware","RequestId":"0HNCULL3CQ9FJ:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.644 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/favicon-32x32.png - 200 628 image/png 54.5137ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FJ:00000002","RequestPath":"/swagger/favicon-32x32.png","ConnectionId":"0HNCULL3CQ9FJ"}
[2025-05-29 18:05:22.806 +05:30 INF] Request finished HTTP/1.1 GET https://localhost:59358/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 240.5526ms {"EventId":{"Id":2},"SourceContext":"Microsoft.AspNetCore.Hosting.Diagnostics","RequestId":"0HNCULL3CQ9FL:00000002","RequestPath":"/swagger/v1/swagger.json","ConnectionId":"0HNCULL3CQ9FL"}
