{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "swagger-ui/custom.2da00dyc2e.css", "AssetFile": "swagger-ui/custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 12:32:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2da00dyc2e"}, {"Name": "integrity", "Value": "sha256-dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU="}, {"Name": "label", "Value": "swagger-ui/custom.css"}]}, {"Route": "swagger-ui/custom.css", "AssetFile": "swagger-ui/custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 12:32:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU="}]}]}