{"Version": 1, "Hash": "6KUtHG2QqVLllAQxexnK611+RLO+0W7pwaZrYoDaTIQ=", "Source": "UBI.CPV.API", "BasePath": "_content/UBI.CPV.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "UBI.CPV.API\\wwwroot", "Source": "UBI.CPV.API", "ContentRoot": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\wwwroot\\", "BasePath": "_content/UBI.CPV.API", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\wwwroot\\swagger-ui\\custom.css", "SourceId": "UBI.CPV.API", "SourceType": "Discovered", "ContentRoot": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\wwwroot\\", "BasePath": "_content/UBI.CPV.API", "RelativePath": "swagger-ui/custom#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2da00dyc2e", "Integrity": "dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\swagger-ui\\custom.css"}], "Endpoints": [{"Route": "swagger-ui/custom.2da00dyc2e.css", "AssetFile": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\wwwroot\\swagger-ui\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 12:32:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2da00dyc2e"}, {"Name": "label", "Value": "swagger-ui/custom.css"}, {"Name": "integrity", "Value": "sha256-dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU="}]}, {"Route": "swagger-ui/custom.css", "AssetFile": "D:\\Augment-projects\\Expired UBI-CPV\\Expired UBI-CPV\\UBI.CPV.API\\wwwroot\\swagger-ui\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 12:32:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dqhPxvVHOEB9yJ4l+QOH1BxIfiTHWWsG6+f+IUm7EiU="}]}]}