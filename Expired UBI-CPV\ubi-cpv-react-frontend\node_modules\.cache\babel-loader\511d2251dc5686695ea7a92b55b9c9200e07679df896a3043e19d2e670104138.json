{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\Expired UBI-CPV\\\\Expired UBI-CPV\\\\ubi-cpv-react-frontend\\\\src\\\\components\\\\Agent\\\\AgentTasks.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { Card, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n_c = FilterContainer;\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c2 = FilterSelect;\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n_c3 = SearchInput;\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n_c4 = TableContainer;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n_c5 = Table;\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n_c7 = TableCell;\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n_c8 = TableRow;\nconst StatusBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.status) {\n    case 'assigned':\n      return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n    case 'in-progress':\n      return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c9 = StatusBadge;\nconst PriorityBadge = styled.span`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n  switch (props.priority) {\n    case 'high':\n      return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n    case 'medium':\n      return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n    case 'low':\n      return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n    default:\n      return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n  }\n}}\n`;\n_c0 = PriorityBadge;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n_c1 = ActionButtons;\nconst AgentTasks = () => {\n  _s();\n  const [tasks, setTasks] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadTasks();\n  }, [statusFilter]);\n  const loadTasks = async () => {\n    try {\n      setLoading(true);\n      const status = statusFilter === 'all' ? undefined : statusFilter;\n      const response = await apiService.getLeads(1, 100, status);\n      setTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading tasks:', error);\n      // Mock data for demo\n      setTasks([{\n        leadId: 1,\n        customerName: 'John Doe',\n        mobileNumber: '9876543210',\n        loanType: 'Personal Loan',\n        status: 'assigned',\n        createdDate: '2024-01-15T10:30:00Z',\n        assignedDate: '2024-01-15T11:00:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Current Agent',\n        documentCount: 0,\n        croppedImageCount: 0\n      }, {\n        leadId: 2,\n        customerName: 'Jane Smith',\n        mobileNumber: '9876543211',\n        loanType: 'Home Loan',\n        status: 'in-progress',\n        createdDate: '2024-01-14T09:15:00Z',\n        assignedDate: '2024-01-14T10:00:00Z',\n        createdByName: 'Admin User',\n        assignedToName: 'Current Agent',\n        documentCount: 2,\n        croppedImageCount: 1\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredTasks = tasks.filter(task => task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || task.mobileNumber.includes(searchTerm) || task.loanType.toLowerCase().includes(searchTerm.toLowerCase()));\n  const navigationItems = [{\n    icon: '🏠',\n    label: 'Dashboard',\n    onClick: () => navigate('/agent/dashboard')\n  }, {\n    icon: '📋',\n    label: 'My Tasks',\n    active: true\n  }, {\n    icon: '✅',\n    label: 'Completed',\n    onClick: () => navigate('/agent/completed')\n  }, {\n    icon: '📊',\n    label: 'Reports',\n    onClick: () => navigate('/agent/reports')\n  }];\n  const handleTaskAction = async (leadId, action) => {\n    try {\n      if (action === 'start') {\n        await apiService.updateLeadStatus(leadId, 'in-progress', 'Started verification process');\n      } else if (action === 'submit') {\n        await apiService.updateLeadStatus(leadId, 'pending-review', 'Submitted for review');\n      }\n      loadTasks(); // Reload tasks after action\n    } catch (error) {\n      console.error('Error updating task:', error);\n      alert('Failed to update task');\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  const getPriority = createdDate => {\n    const daysDiff = Math.floor((Date.now() - new Date(createdDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 3) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n      title: \"My Tasks\",\n      navigationItems: navigationItems,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"My Tasks\",\n    navigationItems: navigationItems,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '20px',\n          color: '#007E3A'\n        },\n        children: \"Assigned Tasks\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterContainer, {\n        children: [/*#__PURE__*/_jsxDEV(FilterSelect, {\n          value: statusFilter,\n          onChange: e => setStatusFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"assigned\",\n            children: \"Assigned\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"in-progress\",\n            children: \"In Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchInput, {\n          type: \"text\",\n          placeholder: \"Search by customer name, mobile, or loan type...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Mobile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Loan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Priority\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Assigned Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableHeader, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredTasks.map(task => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.customerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.mobileNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.loanType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n                  status: task.status,\n                  children: task.status.replace('-', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(PriorityBadge, {\n                  priority: getPriority(task.createdDate),\n                  children: getPriority(task.createdDate).toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: task.assignedDate ? formatDate(task.assignedDate) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(ActionButtons, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    onClick: () => navigate(`/lead/${task.leadId}`),\n                    children: \"View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 23\n                  }, this), task.status === 'assigned' && /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"secondary\",\n                    onClick: () => handleTaskAction(task.leadId, 'start'),\n                    children: \"Start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this), task.status === 'in-progress' && /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline\",\n                    onClick: () => handleTaskAction(task.leadId, 'submit'),\n                    children: \"Submit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, task.leadId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), filteredTasks.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px',\n          color: '#777'\n        },\n        children: searchTerm ? 'No tasks found matching your search.' : 'No tasks assigned yet.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTasks, \"dtPYC52v1QIpFh1N7PnoeyJ+eQQ=\", false, function () {\n  return [useNavigate];\n});\n_c10 = AgentTasks;\nexport default AgentTasks;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"FilterContainer\");\n$RefreshReg$(_c2, \"FilterSelect\");\n$RefreshReg$(_c3, \"SearchInput\");\n$RefreshReg$(_c4, \"TableContainer\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"TableRow\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"PriorityBadge\");\n$RefreshReg$(_c1, \"ActionButtons\");\n$RefreshReg$(_c10, \"AgentTasks\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "DashboardLayout", "Card", "<PERSON><PERSON>", "LoadingSpinner", "apiService", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "FilterSelect", "select", "props", "theme", "colors", "mediumGray", "borderRadius", "sm", "primary", "_c2", "SearchInput", "input", "_c3", "TableContainer", "_c4", "Table", "table", "_c5", "TableHeader", "th", "lightGray", "offWhite", "textMedium", "_c6", "TableCell", "td", "_c7", "TableRow", "tr", "_c8", "StatusBadge", "span", "status", "_c9", "PriorityBadge", "priority", "_c0", "ActionButtons", "_c1", "AgentTasks", "_s", "tasks", "setTasks", "loading", "setLoading", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "navigate", "loadTasks", "undefined", "response", "getLeads", "data", "error", "console", "leadId", "customerName", "mobileNumber", "loanType", "createdDate", "assignedDate", "createdByName", "assignedToName", "documentCount", "croppedImageCount", "filteredTasks", "filter", "task", "toLowerCase", "includes", "navigationItems", "icon", "label", "onClick", "active", "handleTaskAction", "action", "updateLeadStatus", "alert", "formatDate", "dateString", "Date", "toLocaleDateString", "getPriority", "daysDiff", "Math", "floor", "now", "getTime", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "color", "value", "onChange", "e", "target", "type", "placeholder", "map", "replace", "toUpperCase", "size", "variant", "length", "textAlign", "padding", "_c10", "$RefreshReg$"], "sources": ["D:/Augment-projects/Expired UBI-CPV/Expired UBI-CPV/ubi-cpv-react-frontend/src/components/Agent/AgentTasks.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport DashboardLayout from '../Layout/DashboardLayout';\nimport { <PERSON>, Button, LoadingSpinner } from '../../styles/GlobalStyles';\nimport { apiService, LeadListItem } from '../../services/apiService';\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  background: white;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SearchInput = styled.input`\n  flex: 1;\n  min-width: 250px;\n  padding: 8px 12px;\n  border: 1px solid ${props => props.theme.colors.mediumGray};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  font-size: 14px;\n  \n  &:focus {\n    border-color: ${props => props.theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst TableContainer = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHeader = styled.th`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n  background-color: ${props => props.theme.colors.offWhite};\n  font-weight: 600;\n  color: ${props => props.theme.colors.textMedium};\n`;\n\nconst TableCell = styled.td`\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid ${props => props.theme.colors.lightGray};\n`;\n\nconst TableRow = styled.tr`\n  &:hover {\n    background-color: ${props => props.theme.colors.lightGray};\n  }\n`;\n\nconst StatusBadge = styled.span<{ status: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.status) {\n      case 'assigned':\n        return `\n          background-color: #fff3e0;\n          color: #e65100;\n        `;\n      case 'in-progress':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst PriorityBadge = styled.span<{ priority: string }>`\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n\n  ${props => {\n    switch (props.priority) {\n      case 'high':\n        return `\n          background-color: #ffebee;\n          color: #c62828;\n        `;\n      case 'medium':\n        return `\n          background-color: #fff8e1;\n          color: #ff8f00;\n        `;\n      case 'low':\n        return `\n          background-color: #e8f5e9;\n          color: #2e7d32;\n        `;\n      default:\n        return `\n          background-color: #f5f5f5;\n          color: #666;\n        `;\n    }\n  }}\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst AgentTasks: React.FC = () => {\n  const [tasks, setTasks] = useState<LeadListItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadTasks();\n  }, [statusFilter]);\n\n  const loadTasks = async () => {\n    try {\n      setLoading(true);\n      const status = statusFilter === 'all' ? undefined : statusFilter;\n      const response = await apiService.getLeads(1, 100, status);\n      setTasks(response.data || []);\n    } catch (error) {\n      console.error('Error loading tasks:', error);\n      // Mock data for demo\n      setTasks([\n        {\n          leadId: 1,\n          customerName: 'John Doe',\n          mobileNumber: '9876543210',\n          loanType: 'Personal Loan',\n          status: 'assigned',\n          createdDate: '2024-01-15T10:30:00Z',\n          assignedDate: '2024-01-15T11:00:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 0,\n          croppedImageCount: 0,\n        },\n        {\n          leadId: 2,\n          customerName: 'Jane Smith',\n          mobileNumber: '9876543211',\n          loanType: 'Home Loan',\n          status: 'in-progress',\n          createdDate: '2024-01-14T09:15:00Z',\n          assignedDate: '2024-01-14T10:00:00Z',\n          createdByName: 'Admin User',\n          assignedToName: 'Current Agent',\n          documentCount: 2,\n          croppedImageCount: 1,\n        },\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredTasks = tasks.filter(task =>\n    task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    task.mobileNumber.includes(searchTerm) ||\n    task.loanType.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const navigationItems = [\n    { icon: '🏠', label: 'Dashboard', onClick: () => navigate('/agent/dashboard') },\n    { icon: '📋', label: 'My Tasks', active: true },\n    { icon: '✅', label: 'Completed', onClick: () => navigate('/agent/completed') },\n    { icon: '📊', label: 'Reports', onClick: () => navigate('/agent/reports') },\n  ];\n\n  const handleTaskAction = async (leadId: number, action: string) => {\n    try {\n      if (action === 'start') {\n        await apiService.updateLeadStatus(leadId, 'in-progress', 'Started verification process');\n      } else if (action === 'submit') {\n        await apiService.updateLeadStatus(leadId, 'pending-review', 'Submitted for review');\n      }\n      loadTasks(); // Reload tasks after action\n    } catch (error) {\n      console.error('Error updating task:', error);\n      alert('Failed to update task');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getPriority = (createdDate: string) => {\n    const daysDiff = Math.floor((Date.now() - new Date(createdDate).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 3) return 'high';\n    if (daysDiff > 1) return 'medium';\n    return 'low';\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"My Tasks\" navigationItems={navigationItems}>\n        <LoadingSpinner />\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout title=\"My Tasks\" navigationItems={navigationItems}>\n      <Card>\n        <h2 style={{ marginBottom: '20px', color: '#007E3A' }}>Assigned Tasks</h2>\n\n        <FilterContainer>\n          <FilterSelect value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)}>\n            <option value=\"all\">All Tasks</option>\n            <option value=\"assigned\">Assigned</option>\n            <option value=\"in-progress\">In Progress</option>\n          </FilterSelect>\n          \n          <SearchInput\n            type=\"text\"\n            placeholder=\"Search by customer name, mobile, or loan type...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </FilterContainer>\n\n        <TableContainer>\n          <Table>\n            <thead>\n              <tr>\n                <TableHeader>Customer</TableHeader>\n                <TableHeader>Mobile</TableHeader>\n                <TableHeader>Loan Type</TableHeader>\n                <TableHeader>Status</TableHeader>\n                <TableHeader>Priority</TableHeader>\n                <TableHeader>Assigned Date</TableHeader>\n                <TableHeader>Actions</TableHeader>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredTasks.map((task) => (\n                <TableRow key={task.leadId}>\n                  <TableCell>{task.customerName}</TableCell>\n                  <TableCell>{task.mobileNumber}</TableCell>\n                  <TableCell>{task.loanType}</TableCell>\n                  <TableCell>\n                    <StatusBadge status={task.status}>\n                      {task.status.replace('-', ' ').toUpperCase()}\n                    </StatusBadge>\n                  </TableCell>\n                  <TableCell>\n                    <PriorityBadge priority={getPriority(task.createdDate)}>\n                      {getPriority(task.createdDate).toUpperCase()}\n                    </PriorityBadge>\n                  </TableCell>\n                  <TableCell>\n                    {task.assignedDate ? formatDate(task.assignedDate) : '-'}\n                  </TableCell>\n                  <TableCell>\n                    <ActionButtons>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => navigate(`/lead/${task.leadId}`)}\n                      >\n                        View\n                      </Button>\n                      {task.status === 'assigned' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleTaskAction(task.leadId, 'start')}\n                        >\n                          Start\n                        </Button>\n                      )}\n                      {task.status === 'in-progress' && (\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleTaskAction(task.leadId, 'submit')}\n                        >\n                          Submit\n                        </Button>\n                      )}\n                    </ActionButtons>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        </TableContainer>\n\n        {filteredTasks.length === 0 && (\n          <div style={{ textAlign: 'center', padding: '40px', color: '#777' }}>\n            {searchTerm ? 'No tasks found matching your search.' : 'No tasks assigned yet.'}\n          </div>\n        )}\n      </Card>\n    </DashboardLayout>\n  );\n};\n\nexport default AgentTasks;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,2BAA2B;AACxE,SAASC,UAAU,QAAsB,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,eAAe,GAAGR,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,YAAY,GAAGX,MAAM,CAACY,MAAM;AAClC;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACC,GAAA,GAXIT,YAAY;AAalB,MAAMU,WAAW,GAAGrB,MAAM,CAACsB,KAAK;AAChC;AACA;AACA;AACA,sBAAsBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,mBAAmBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA,oBAAoBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,OAAO;AACvD;AACA;AACA,CAAC;AAACI,GAAA,GAZIF,WAAW;AAcjB,MAAMG,cAAc,GAAGxB,MAAM,CAACS,GAAG;AACjC;AACA,CAAC;AAACgB,GAAA,GAFID,cAAc;AAIpB,MAAME,KAAK,GAAG1B,MAAM,CAAC2B,KAAK;AAC1B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,WAAW,GAAG7B,MAAM,CAAC8B,EAAE;AAC7B;AACA;AACA,6BAA6BjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAClE,sBAAsBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,QAAQ;AAC1D;AACA,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACkB,UAAU;AACjD,CAAC;AAACC,GAAA,GAPIL,WAAW;AASjB,MAAMM,SAAS,GAAGnC,MAAM,CAACoC,EAAE;AAC3B;AACA;AACA,6BAA6BvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAClE,CAAC;AAACM,GAAA,GAJIF,SAAS;AAMf,MAAMG,QAAQ,GAAGtC,MAAM,CAACuC,EAAE;AAC1B;AACA,wBAAwB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,SAAS;AAC7D;AACA,CAAC;AAACS,GAAA,GAJIF,QAAQ;AAMd,MAAMG,WAAW,GAAGzC,MAAM,CAAC0C,IAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI7B,KAAK,IAAI;EACT,QAAQA,KAAK,CAAC8B,MAAM;IAClB,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,aAAa;MAChB,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA1BIH,WAAW;AA4BjB,MAAMI,aAAa,GAAG7C,MAAM,CAAC0C,IAA0B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI7B,KAAK,IAAI;EACT,QAAQA,KAAK,CAACiC,QAAQ;IACpB,KAAK,MAAM;MACT,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA/BIF,aAAa;AAiCnB,MAAMG,aAAa,GAAGhD,MAAM,CAACS,GAAG;AAChC;AACA;AACA,CAAC;AAACwC,GAAA,GAHID,aAAa;AAKnB,MAAME,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM+D,QAAQ,GAAG7D,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd+D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EAElB,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMZ,MAAM,GAAGa,YAAY,KAAK,KAAK,GAAGM,SAAS,GAAGN,YAAY;MAChE,MAAMO,QAAQ,GAAG,MAAM1D,UAAU,CAAC2D,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAErB,MAAM,CAAC;MAC1DU,QAAQ,CAACU,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAb,QAAQ,CAAC,CACP;QACEe,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,UAAU;QACxBC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,eAAe;QACzB5B,MAAM,EAAE,UAAU;QAClB6B,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACET,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,YAAY;QAC1BC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,WAAW;QACrB5B,MAAM,EAAE,aAAa;QACrB6B,WAAW,EAAE,sBAAsB;QACnCC,YAAY,EAAE,sBAAsB;QACpCC,aAAa,EAAE,YAAY;QAC3BC,cAAc,EAAE,eAAe;QAC/BC,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAG1B,KAAK,CAAC2B,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACX,YAAY,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,IAClED,IAAI,CAACV,YAAY,CAACY,QAAQ,CAACxB,UAAU,CAAC,IACtCsB,IAAI,CAACT,QAAQ,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAC/D,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC/E;IAAEwB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEE,MAAM,EAAE;EAAK,CAAC,EAC/C;IAAEH,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,WAAW;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB;EAAE,CAAC,EAC9E;IAAEwB,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,gBAAgB;EAAE,CAAC,CAC5E;EAED,MAAM4B,gBAAgB,GAAG,MAAAA,CAAOpB,MAAc,EAAEqB,MAAc,KAAK;IACjE,IAAI;MACF,IAAIA,MAAM,KAAK,OAAO,EAAE;QACtB,MAAMpF,UAAU,CAACqF,gBAAgB,CAACtB,MAAM,EAAE,aAAa,EAAE,8BAA8B,CAAC;MAC1F,CAAC,MAAM,IAAIqB,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAMpF,UAAU,CAACqF,gBAAgB,CAACtB,MAAM,EAAE,gBAAgB,EAAE,sBAAsB,CAAC;MACrF;MACAP,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CyB,KAAK,CAAC,uBAAuB,CAAC;IAChC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,WAAW,GAAIxB,WAAmB,IAAK;IAC3C,MAAMyB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,IAAIN,IAAI,CAACtB,WAAW,CAAC,CAAC6B,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACnG,IAAIJ,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAM;IAC/B,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,QAAQ;IACjC,OAAO,KAAK;EACd,CAAC;EAED,IAAI3C,OAAO,EAAE;IACX,oBACE/C,OAAA,CAACN,eAAe;MAACqG,KAAK,EAAC,UAAU;MAACnB,eAAe,EAAEA,eAAgB;MAAAoB,QAAA,eACjEhG,OAAA,CAACH,cAAc;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEtB;EAEA,oBACEpG,OAAA,CAACN,eAAe;IAACqG,KAAK,EAAC,UAAU;IAACnB,eAAe,EAAEA,eAAgB;IAAAoB,QAAA,eACjEhG,OAAA,CAACL,IAAI;MAAAqG,QAAA,gBACHhG,OAAA;QAAIqG,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAP,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE1EpG,OAAA,CAACC,eAAe;QAAA+F,QAAA,gBACdhG,OAAA,CAACI,YAAY;UAACoG,KAAK,EAAEvD,YAAa;UAACwD,QAAQ,EAAGC,CAAC,IAAKxD,eAAe,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAR,QAAA,gBAClFhG,OAAA;YAAQwG,KAAK,EAAC,KAAK;YAAAR,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCpG,OAAA;YAAQwG,KAAK,EAAC,UAAU;YAAAR,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CpG,OAAA;YAAQwG,KAAK,EAAC,aAAa;YAAAR,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEfpG,OAAA,CAACc,WAAW;UACV8F,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,kDAAkD;UAC9DL,KAAK,EAAErD,UAAW;UAClBsD,QAAQ,EAAGC,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAElBpG,OAAA,CAACiB,cAAc;QAAA+E,QAAA,eACbhG,OAAA,CAACmB,KAAK;UAAA6E,QAAA,gBACJhG,OAAA;YAAAgG,QAAA,eACEhG,OAAA;cAAAgG,QAAA,gBACEhG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxCpG,OAAA,CAACsB,WAAW;gBAAA0E,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpG,OAAA;YAAAgG,QAAA,EACGzB,aAAa,CAACuC,GAAG,CAAErC,IAAI,iBACtBzE,OAAA,CAAC+B,QAAQ;cAAAiE,QAAA,gBACPhG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,EAAEvB,IAAI,CAACX;cAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,EAAEvB,IAAI,CAACV;cAAY;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,EAAEvB,IAAI,CAACT;cAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtCpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,eACRhG,OAAA,CAACkC,WAAW;kBAACE,MAAM,EAAEqC,IAAI,CAACrC,MAAO;kBAAA4D,QAAA,EAC9BvB,IAAI,CAACrC,MAAM,CAAC2E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACZpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,eACRhG,OAAA,CAACsC,aAAa;kBAACC,QAAQ,EAAEkD,WAAW,CAAChB,IAAI,CAACR,WAAW,CAAE;kBAAA+B,QAAA,EACpDP,WAAW,CAAChB,IAAI,CAACR,WAAW,CAAC,CAAC+C,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACZpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,EACPvB,IAAI,CAACP,YAAY,GAAGmB,UAAU,CAACZ,IAAI,CAACP,YAAY,CAAC,GAAG;cAAG;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACZpG,OAAA,CAAC4B,SAAS;gBAAAoE,QAAA,eACRhG,OAAA,CAACyC,aAAa;kBAAAuD,QAAA,gBACZhG,OAAA,CAACJ,MAAM;oBACLqH,IAAI,EAAC,IAAI;oBACTlC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,SAASoB,IAAI,CAACZ,MAAM,EAAE,CAAE;oBAAAmC,QAAA,EACjD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACR3B,IAAI,CAACrC,MAAM,KAAK,UAAU,iBACzBpC,OAAA,CAACJ,MAAM;oBACLqH,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAC,WAAW;oBACnBnC,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAACR,IAAI,CAACZ,MAAM,EAAE,OAAO,CAAE;oBAAAmC,QAAA,EACvD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EACA3B,IAAI,CAACrC,MAAM,KAAK,aAAa,iBAC5BpC,OAAA,CAACJ,MAAM;oBACLqH,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAC,SAAS;oBACjBnC,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAACR,IAAI,CAACZ,MAAM,EAAE,QAAQ,CAAE;oBAAAmC,QAAA,EACxD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA5CC3B,IAAI,CAACZ,MAAM;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6ChB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhB7B,aAAa,CAAC4C,MAAM,KAAK,CAAC,iBACzBnH,OAAA;QAAKqG,KAAK,EAAE;UAAEe,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEd,KAAK,EAAE;QAAO,CAAE;QAAAP,QAAA,EACjE7C,UAAU,GAAG,sCAAsC,GAAG;MAAwB;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEtB,CAAC;AAACxD,EAAA,CAjMID,UAAoB;EAAA,QAKPnD,WAAW;AAAA;AAAA8H,IAAA,GALxB3E,UAAoB;AAmM1B,eAAeA,UAAU;AAAC,IAAAxC,EAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA4E,IAAA;AAAAC,YAAA,CAAApH,EAAA;AAAAoH,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}